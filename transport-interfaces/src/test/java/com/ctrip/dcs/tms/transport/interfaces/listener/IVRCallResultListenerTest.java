package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.DrvInActiveEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IVRCallResultListenerTest {

    @InjectMocks
    private IVRCallResultListener ivrCallResultListener;

    @Mock
    private DriverDomainService driverDomainService;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private TmsDrvInactiveReasonRepository tmsDrvInactiveReasonRepository;

    private Message message;
    private Long drvId = 123L;
    private String callTaskId = "456";
    private String phoneNumber = "13800138000";
    private QueryCallPhoneForVerifyResultResponseType responseType;

    @Before
    public void setUp() {
        // 创建基本消息
        message = new BaseMessage("test", TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION);
        message.setProperty("drvId", drvId);
        message.setProperty("callTaskId", callTaskId);
        message.setProperty("phoneNumber", phoneNumber);

        // 创建响应对象
        responseType = new QueryCallPhoneForVerifyResultResponseType();
        responseType.setCallResultStatus("0"); // 默认为成功状态

        // 设置默认的mock行为
        when(driverDomainService.queryCallPhoneForVerifyResult(any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(responseType);
        when(commonConfig.getCallResultStatus()).thenReturn(Collections.singletonList("0")); // 默认成功状态为"0"
    }

    @Test
    public void testHandleIVRCallResult_MissingParameters() {
        // 创建缺少参数的消息
        Message invalidMessage = new BaseMessage("test", TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION);

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(invalidMessage);

        // 验证不调用任何服务
        verify(driverDomainService, never()).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, never()).query(anyList());
        verify(tmsDrvInactiveReasonRepository, never()).deleteReasonByCode(anyLong(), any(), anyString());
        verify(drvDrvierRepository, never()).updateDrvStatus(anyList(), any(), any(), anyString());
    }

    @Test
    public void testHandleIVRCallResult_CallConnected_NoOtherReasons() {
        // 设置mock行为
        List<TmsDrvInactiveReasonPO> inactiveReasons = new ArrayList<>();
        TmsDrvInactiveReasonPO notLoginReason = TmsDrvInactiveReasonPO.builder()
                .drvId(drvId)
                .reasonCode(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())
                .reasonDesc("Not login")
                .active(true)
                .build();
        inactiveReasons.add(notLoginReason);

        when(tmsDrvInactiveReasonRepository.query(Lists.newArrayList(drvId))).thenReturn(inactiveReasons);
        when(tmsDrvInactiveReasonRepository.deleteReasonByCode(eq(drvId), eq(Lists.newArrayList(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())), anyString())).thenReturn(1);

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, times(1)).query(Lists.newArrayList(drvId));
        verify(tmsDrvInactiveReasonRepository, times(1)).deleteReasonByCode(eq(drvId), eq(Lists.newArrayList(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())), eq("system"));

        // 验证司机状态更新
        ArgumentCaptor<List<Long>> drvIdsCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<Integer> statusCaptor = ArgumentCaptor.forClass(Integer.class);
        verify(drvDrvierRepository, times(1)).updateDrvStatus(drvIdsCaptor.capture(), eq(null), statusCaptor.capture(), eq("system"));

        assertEquals(Collections.singletonList(drvId), drvIdsCaptor.getValue());
        assertEquals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), statusCaptor.getValue());
    }

    @Test
    public void testHandleIVRCallResult_CallConnected_WithOtherReasons() {
        // 设置mock行为 - 有其他未激活原因
        List<TmsDrvInactiveReasonPO> inactiveReasons = new ArrayList<>();
        TmsDrvInactiveReasonPO notLoginReason = TmsDrvInactiveReasonPO.builder()
                .drvId(drvId)
                .reasonCode(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())
                .reasonDesc("Not login")
                .active(true)
                .build();
        TmsDrvInactiveReasonPO otherReason = TmsDrvInactiveReasonPO.builder()
                .drvId(drvId)
                .reasonCode(DrvInActiveEnum.ACCOUNT_REGISTER_ERROR.getCode())
                .reasonDesc("Register account error")
                .active(true)
                .build();
        inactiveReasons.add(notLoginReason);
        inactiveReasons.add(otherReason);

        when(tmsDrvInactiveReasonRepository.query(Lists.newArrayList(drvId))).thenReturn(inactiveReasons);
        when(tmsDrvInactiveReasonRepository.deleteReasonByCode(eq(drvId), eq(Lists.newArrayList(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())), anyString())).thenReturn(1);

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, times(1)).query(Lists.newArrayList(drvId));
        verify(tmsDrvInactiveReasonRepository, times(1)).deleteReasonByCode(eq(drvId), eq(Lists.newArrayList(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())), eq("system"));

        // 验证司机状态不更新
        verify(drvDrvierRepository, never()).updateDrvStatus(anyList(), any(), any(), anyString());
    }

    @Test
    public void testHandleIVRCallResult_CallNotConnected() {
        // 设置mock行为 - 电话未接通
        responseType.setCallResultStatus("1"); // 假设"1"表示未接通
        when(commonConfig.getCallResultStatus()).thenReturn(Collections.singletonList("0")); // 只有"0"表示接通

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, never()).query(anyList());
        verify(tmsDrvInactiveReasonRepository, never()).deleteReasonByCode(anyLong(), any(), anyString());

        // 验证添加未激活原因
        ArgumentCaptor<TmsDrvInactiveReasonPO> reasonCaptor = ArgumentCaptor.forClass(TmsDrvInactiveReasonPO.class);
        verify(tmsDrvInactiveReasonRepository, times(1)).insert(reasonCaptor.capture());

        TmsDrvInactiveReasonPO capturedReason = reasonCaptor.getValue();
        assertEquals(drvId, capturedReason.getDrvId());
        assertEquals(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode(), capturedReason.getReasonCode());
        assertEquals(true, capturedReason.getActive());
    }

    @Test
    public void testHandleIVRCallResult_NullResponse() {
        // 设置mock行为 - 响应为null
        when(driverDomainService.queryCallPhoneForVerifyResult(any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(null);

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, never()).query(anyList());
        verify(tmsDrvInactiveReasonRepository, never()).deleteReasonByCode(anyLong(), any(), anyString());
        verify(tmsDrvInactiveReasonRepository, never()).insert(any());
        verify(drvDrvierRepository, never()).updateDrvStatus(anyList(), any(), any(), anyString());
    }

    @Test
    public void testHandleIVRCallResult_ExceptionInActivation() {
        // 设置mock行为 - 查询未激活原因时抛出异常
        when(tmsDrvInactiveReasonRepository.query(anyList())).thenThrow(new RuntimeException("Test exception"));

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, times(1)).query(Lists.newArrayList(drvId));
        verify(tmsDrvInactiveReasonRepository, never()).deleteReasonByCode(anyLong(), any(), anyString());
        verify(drvDrvierRepository, never()).updateDrvStatus(anyList(), any(), any(), anyString());
    }

    @Test
    public void testHandleIVRCallResult_ExceptionInInsertReason() {
        // 设置mock行为 - 电话未接通，但插入未激活原因时抛出异常
        responseType.setCallResultStatus("1"); // 假设"1"表示未接通
        when(commonConfig.getCallResultStatus()).thenReturn(Collections.singletonList("0")); // 只有"0"表示接通
        when(tmsDrvInactiveReasonRepository.insert(any())).thenThrow(new RuntimeException("Test exception"));

        // 执行测试
        ivrCallResultListener.handleIVRCallResult(message);

        // 验证调用
        verify(driverDomainService, times(1)).queryCallPhoneForVerifyResult(any());
        verify(tmsDrvInactiveReasonRepository, times(1)).insert(any());
        verify(tmsDrvInactiveReasonRepository, never()).query(anyList());
        verify(tmsDrvInactiveReasonRepository, never()).deleteReasonByCode(anyLong(), any(), anyString());
        verify(drvDrvierRepository, never()).updateDrvStatus(anyList(), any(), any(), anyString());
    }
}
