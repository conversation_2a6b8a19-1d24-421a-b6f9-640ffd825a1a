package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.*;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.collect.*;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 运力组列表查询
 * <AUTHOR>
 * @Date 2020/3/3 19:17
 */
@Component
public class QueryTransportGroupListExecutor extends AbstractRpcExecutor<QueryTransportGroupListSOARequestType, QueryTransportGroupListSOAResponseType> implements Validator<QueryTransportGroupListSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    private ContractRepository contractRepository;

    @Override
    public QueryTransportGroupListSOAResponseType execute(QueryTransportGroupListSOARequestType queryTransportGroupListSOARequestType) {
        QueryTransportGroupListSOAResponseType soaResponseType = new QueryTransportGroupListSOAResponseType();
        TspTransportGroupPO transportGroupPO = requestTypeToPO(queryTransportGroupListSOARequestType);
        Result<PageHolder<TspTransportGroupPO>> pageHolderResult = transportGroupQueryService.queryTransportGroupList(transportGroupPO,queryTransportGroupListSOARequestType);
        List<TransportGroupListSOAType> list = Lists.newArrayList();
        for (TspTransportGroupPO tspTransportGroupPO : pageHolderResult.getData().getData()) {
            list.add(poToListType(tspTransportGroupPO));
        }
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        soaResponseType.setPagination(paginationDTO);
        soaResponseType.setData(list);
        return ServiceResponseUtils.success(soaResponseType);
    }

    private TspTransportGroupPO requestTypeToPO(QueryTransportGroupListSOARequestType requestType){
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        BeanUtils.copyProperties(requestType,transportGroupPO);
        transportGroupPO.setSupplierId(requestType.getSupplierId());
        transportGroupPO.setTransportGroupId(requestType.getTransportGroupId());
        transportGroupPO.setTransportGroupMode(requestType.getTransportGroupMode());
        transportGroupPO.setTransportGroupName(requestType.getTransportGroupName());
        transportGroupPO.setGroupStatus(requestType.getGroupStatus());
        if (requestType.getCityId() != null) {
            transportGroupPO.setPointCityId(Long.valueOf(requestType.getCityId()));
        }
        transportGroupPO.setDispatcherLanguage(requestType.getDispatcherLanguage());
        if (requestType.getProLineId() != null) {
            transportGroupPO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(Lists.newArrayList(requestType.getProLineId())));
        }
        transportGroupPO.setShortTransportGroup(requestType.getShortTransportGroup());
        return transportGroupPO;
    }

    public TransportGroupListSOAType poToListType(TspTransportGroupPO po){
        TransportGroupListSOAType transportGroupListType = new TransportGroupListSOAType();
        BeanUtils.copyProperties(po,transportGroupListType);
        transportGroupListType.setTransportGroupId(po.getTransportGroupId());
        transportGroupListType.setSupplierId(po.getSupplierId());
        String supplierName = enumRepository.getSupplierName(po.getSupplierId());
        transportGroupListType.setSupplierName(supplierName);
        transportGroupListType.setTransportGroupName(po.getTransportGroupName());
        transportGroupListType.setTransportGroupMode(po.getTransportGroupMode());
        transportGroupListType.setTransportGroupModeName(enumRepository.getTransportGroupMode().get(po.getTransportGroupMode()));
//        Set<String> set = StringUtils.commaDelimitedListToSet(po.getCityId());
//        Set<Long> cityIds = Sets.newHashSet();
//        for (String cityId : set) {
//            cityIds.add(Long.valueOf(cityId));
//        }
//        Map<Long, String> cityNames = enumRepository.getCityNames(cityIds);
        Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
        transportGroupListType.setCityId(String.valueOf(po.getPointCityId()));
        transportGroupListType.setCityName(enumRepository.getCityName(po.getPointCityId()));
        transportGroupListType.setDispatcherLanguage(po.getDispatcherLanguage());
        transportGroupListType.setDispatcherLanguageName(enumRepository.getDrvLanguageName(po.getDispatcherLanguage(), languageMap));
        transportGroupListType.setGroupStatus(po.getGroupStatus());
        transportGroupListType.setGroupStatusName(enumRepository.getTransportGroupStatusMap().get(po.getGroupStatus()));
        transportGroupListType.setCreateUser(po.getCreateUser());
        transportGroupListType.setModifyUser(po.getModifyUser());
//        LocalDateTime localDateTime = LocalDateTime.ofInstant(po.getDatachangeCreatetime().toInstant(), ZoneId.systemDefault());
        transportGroupListType.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
        transportGroupListType.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
        transportGroupListType.setCreateUser(po.getCreateUser());
        transportGroupListType.setModifyUser(po.getModifyUser());
        transportGroupListType.setIgtCode(po.getIgtCode());

        Contract contract = contractRepository.findOne(po.getContractId());
        if (contract != null && contract.getSalesMode() != null) {
            transportGroupListType.setSalesModelName(SharkUtil.getSalesModeName(contract.getSalesMode()));
        }
        ServiceProvider serviceProvider =  enumRepository.getServiceProviderByContractId(po.getContractId());
        transportGroupListType.setBrandName(Objects.isNull(serviceProvider) ? "" : serviceProvider.getBrandLocalName());
        transportGroupListType.setVehicleTypeName(enumRepository.getVehicleTypeName(po.getVehicleTypeId()));
        transportGroupListType.setProLineName(productionLineUtil.getProductionLineNames(po.getCategorySynthesizeCode()));
        List<Integer> showIdList = productionLineUtil.getShowProductionLineList(po.getCategorySynthesizeCode());
        if (CollectionUtils.isNotEmpty(showIdList)) {
            transportGroupListType.setProLineId(showIdList.get(0));
        }
        transportGroupListType.setBrandId(Objects.isNull(serviceProvider) ? 0L : serviceProvider.getId() == null ? 0L : serviceProvider.getId());
        transportGroupListType.setIsOverseasCity(enumRepository.getAreaScope(po.getPointCityId()));
        return transportGroupListType;
    }

}
