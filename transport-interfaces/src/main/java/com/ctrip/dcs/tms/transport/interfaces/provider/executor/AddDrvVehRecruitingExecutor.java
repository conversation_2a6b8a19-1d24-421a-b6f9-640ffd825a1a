package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 添加司机车辆审批
 */
@Component
public class AddDrvVehRecruitingExecutor extends AbstractRpcExecutor<DrvVehRecruitingAddSOARequestType, DrvVehRecruitingAddSOAResponseType> implements Validator<DrvVehRecruitingAddSOARequestType> {

    @Autowired
    private DrvVehRecruitingCommandService commandService;

    @Override
    public DrvVehRecruitingAddSOAResponseType execute(DrvVehRecruitingAddSOARequestType requestType) {
        DrvVehRecruitingAddSOAResponseType responseType = new DrvVehRecruitingAddSOAResponseType();
        Result<Boolean> result = commandService.addDrvVehRecruiting(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }
}
