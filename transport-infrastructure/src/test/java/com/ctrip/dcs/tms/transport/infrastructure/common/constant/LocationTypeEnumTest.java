package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * LocationTypeEnum 测试类
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class LocationTypeEnumTest {

    @Test
    public void testEnumValues() {
        // 测试枚举值
        assertEquals("city", LocationTypeEnum.CITY.getCode());
        assertEquals("城市", LocationTypeEnum.CITY.getDescription());
        
        assertEquals("country", LocationTypeEnum.COUNTRY.getCode());
        assertEquals("国家", LocationTypeEnum.COUNTRY.getDescription());
    }

    @Test
    public void testFromCode() {
        // 测试根据code获取枚举
        assertEquals(LocationTypeEnum.CITY, LocationTypeEnum.fromCode("city"));
        assertEquals(LocationTypeEnum.COUNTRY, LocationTypeEnum.fromCode("country"));
        
        // 测试不存在的code
        assertNull(LocationTypeEnum.fromCode("unknown"));
        assertNull(LocationTypeEnum.fromCode(null));
    }

    @Test
    public void testEnumCount() {
        // 测试枚举数量
        LocationTypeEnum[] values = LocationTypeEnum.values();
        assertEquals(2, values.length);
    }
}
