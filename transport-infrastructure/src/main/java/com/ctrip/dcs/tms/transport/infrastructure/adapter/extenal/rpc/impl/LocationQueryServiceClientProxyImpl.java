package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.location.service.interfaces.message.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 */
@Service
public class LocationQueryServiceClientProxyImpl implements LocationQueryServiceClientProxy {

    @Autowired
    private LocationQueryService LocationQueryService;

    @Override
    public QueryDriverLocationResponseType queryDriverLocation(QueryDriverLocationRequestType request) {
        return LocationQueryService.queryDriverLocation(request);
    }
}
