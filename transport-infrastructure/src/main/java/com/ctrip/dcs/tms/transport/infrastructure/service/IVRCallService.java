package com.ctrip.dcs.tms.transport.infrastructure.service;

/**
 * IVR电话呼叫服务接口
 * 负责处理IVR电话呼叫相关的业务逻辑
 */
public interface IVRCallService {
    
    /**
     * 发起IVR电话验证
     * 如果Redis中已存在该手机号的taskId，会先检查该taskId的状态
     * 如果状态是success或pending，直接返回该taskId
     * 如果状态是fail，删除Redis中的taskId，重新发起IVR呼叫
     * 如果Redis中不存在taskId，直接发起IVR呼叫
     *
     * @param mobilePhone 手机号
     * @param countryCode 国家码
     * @param channel 渠道，如"tms_driver_register"
     * @return IVR任务ID
     */
    Long callPhoneForVerify(String mobilePhone, String countryCode, String channel);
}
