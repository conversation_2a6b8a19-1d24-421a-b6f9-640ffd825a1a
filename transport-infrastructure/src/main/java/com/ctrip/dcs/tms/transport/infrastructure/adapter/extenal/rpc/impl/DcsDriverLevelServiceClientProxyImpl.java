package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsQry;
import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsResp;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsDriverLevelService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsDriverLevelServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.LocationQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DcsDriverLevelServiceClientProxyImpl implements DcsDriverLevelServiceClientProxy {

    @Autowired
    private DcsDriverLevelService dcsDriverLevelService;

    @Override
    public BatchDriverPointsResp queryDriverBatchPoints(BatchDriverPointsQry request) {
        return dcsDriverLevelService.queryDriverBatchPoints(request);
    }
}
