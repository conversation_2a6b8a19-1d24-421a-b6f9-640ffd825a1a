package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.base.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.Objects;
import java.util.*;

@Repository(value = "tmsTransportApproveRepository")
public class TmsTransportApproveRepositoryImpl implements TmsTransportApproveRepository {

    private DalRepository<TmsTransportApprovePO> tmsTransportApproveRepo;

    private DalRowMapper<TmsTransportApprovePO> tmsTransportApprovePODalRowMapper;


    public TmsTransportApproveRepositoryImpl() throws SQLException {
        this.tmsTransportApprovePODalRowMapper = new DalDefaultJpaMapper<>(TmsTransportApprovePO.class);
        tmsTransportApproveRepo = new DalRepositoryImpl<>(TmsTransportApprovePO.class);
    }

    @Override
    public TmsTransportApprovePO queryByPk(Long id) {
        return tmsTransportApproveRepo.queryByPk(id);
    }

    @Override
    public Long insertTmsTransportApprovePO(TmsTransportApprovePO approvePO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsTransportApproveRepo.insert(new DalHints(), keyHolder, approvePO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public int countQueryApproveList(QueryApproveListDO approveListDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(approveListDO.getEventType()!=null && approveListDO.getEventType() > 0){
                builder.equal("event_type", approveListDO.getEventType(), Types.INTEGER);
            }
            if(StringUtils.isNotEmpty(approveListDO.getApproveName())){
                builder.and().like("approve_name", approveListDO.getApproveName()+"%", Types.VARCHAR);
            }
            if(approveListDO.getSupplierId()!=null && approveListDO.getSupplierId() > 0){
                builder.and().equal("supplier_id",approveListDO.getSupplierId(),Types.BIGINT);
            }
            if(approveListDO.getApproveStatus()!=null){
                builder.and().equal("approve_status",approveListDO.getApproveStatus(),Types.INTEGER);
            }
            if (approveListDO.getApproveSourceId() != null) {
                builder.and().equal("approve_source_id",approveListDO.getApproveSourceId(),Types.BIGINT);
            }
            String accountId = StringUtils.isEmpty(approveListDO.getAccountId())?"":approveListDO.getAccountId();
            if(approveListDO.getOneSelfStatus()!=null && approveListDO.getOneSelfStatus()==1){
                builder.and().equal("account_id",accountId,Types.VARCHAR);
            }else if(approveListDO.getOneSelfStatus()!=null && approveListDO.getOneSelfStatus()==0){
                builder.and().notEqual("account_id",accountId,Types.VARCHAR);
            }
            return tmsTransportApproveRepo.getDao().count(builder,hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsTransportApprovePO> queryApproveList(QueryApproveListDO approveListDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(approveListDO.getEventType()!=null && approveListDO.getEventType() > 0){
                builder.equal("event_type", approveListDO.getEventType(), Types.INTEGER);
            }
            if(StringUtils.isNotEmpty(approveListDO.getApproveName())){
                builder.and().like("approve_name", approveListDO.getApproveName()+"%", Types.VARCHAR);
            }
            if(approveListDO.getSupplierId()!=null && approveListDO.getSupplierId() > 0){
                builder.and().equal("supplier_id",approveListDO.getSupplierId(),Types.BIGINT);
            }
            if(approveListDO.getApproveStatus()!=null){
                builder.and().equal("approve_status",approveListDO.getApproveStatus(),Types.INTEGER);
            }
            if (approveListDO.getApproveSourceId() != null) {
                builder.and().equal("approve_source_id",approveListDO.getApproveSourceId(),Types.BIGINT);
            }
            String accountId = StringUtils.isEmpty(approveListDO.getAccountId())?"":approveListDO.getAccountId();
            if(approveListDO.getOneSelfStatus()!=null && approveListDO.getOneSelfStatus()==1){
                builder.and().equal("account_id",accountId,Types.VARCHAR);
            }else if(approveListDO.getOneSelfStatus()!=null && approveListDO.getOneSelfStatus()==0){
                builder.and().notEqual("account_id",accountId,Types.VARCHAR);
            }
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(approveListDO.getPageNo(),approveListDO.getPageSize());
            return tmsTransportApproveRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveStatus(UpdateApproveStatusDO statusDO) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_transport_approve set approve_status = ?,modify_user = ?,remarks = ?,approve_schedule = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_status", Types.INTEGER, statusDO.getApproveStatus());
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, statusDO.getModifyUser());
            parameters.setSensitive(i++, "remarks", Types.VARCHAR, StringUtils.isEmpty(statusDO.getRemarks())?"":statusDO.getRemarks());
            parameters.setSensitive(i++, "approve_schedule", Types.TINYINT, 1);
            if(StringUtils.isNotEmpty(statusDO.getCertificateCheckResult())){
                sqlStr.append(",certificate_check_result = ? ");
                parameters.set(i++, "certificate_check_result", Types.VARCHAR, statusDO.getCertificateCheckResult());
            }
            sqlStr.append(" WHERE id in (?) ");
            parameters.setInParameter(i++, "id", Types.BIGINT, statusDO.getIds());
            builder.setTemplate(sqlStr.toString());
            return tmsTransportApproveRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean checkIsApproveIng(Long approveSourceId, Integer approveSourceType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            builder.and().in("approve_status", Arrays.asList(0,1), Types.INTEGER);
            return tmsTransportApproveRepo.getDao().count(builder,hints).intValue() > 0?Boolean.TRUE:Boolean.FALSE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsTransportApprovePO> queryApproveListByIds(List<Long> ids) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("id", ids, Types.BIGINT);
            builder.orderBy("datachange_lasttime", false);
            return tmsTransportApproveRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCheckResult(Long id, String certificateCheckResult, String modifyUser) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_transport_approve set certificate_check_result = ?,modify_user = ? where id = ?");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "certificate_check_result", Types.VARCHAR, certificateCheckResult);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)? TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            return tmsTransportApproveRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsTransportApprovePO> queryApproveBySourceId(Long approveSourceId, Integer approveSourceType, Integer approveStatus,Integer eventType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            builder.and().equal("approve_status", approveStatus, Types.INTEGER);
            builder.and().equal("event_type",eventType,Types.INTEGER);
            builder.orderBy("datachange_lasttime", false);
            return tmsTransportApproveRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsTransportApprovePO> queryApproveIngBySourceId(List<Long> approveSourceIds, Integer approveSourceType, Integer approveStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(CollectionUtils.isNotEmpty(approveSourceIds)){
                builder.in("approve_source_id", approveSourceIds, Types.BIGINT);
            }
            if(!Objects.isNull(approveSourceType)){
                builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            }
            builder.and().equal("approve_status", approveStatus, Types.INTEGER);
            builder.orderBy("datachange_lasttime", false);
            return tmsTransportApproveRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public TmsTransportApprovePO queryApproveRemarks(Long approveSourceId, Integer approveSourceType, Integer approveStatus, Integer eventType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("approve_source_id","remarks");
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            builder.and().equal("approve_status", approveStatus, Types.INTEGER);
            builder.and().equal("event_type", eventType, Types.INTEGER);
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(1,1);
            List<TmsTransportApprovePO> approvePOS = tmsTransportApproveRepo.getDao().query(builder,hints);
            if (CollectionUtils.isEmpty(approvePOS)) {
                return new TmsTransportApprovePO();
            }
            return approvePOS.get(0);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCheckResultAndFinishStatus(Long id, String certificateCheckResult, Integer approveSchedule, String modifyUser) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_transport_approve set certificate_check_result = ?,modify_user = ?, approve_schedule = ? where id = ?");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "certificate_check_result", Types.VARCHAR, certificateCheckResult);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)? TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setSensitive(i++, "approve_schedule", Types.TINYINT, approveSchedule);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            return tmsTransportApproveRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryApproveCountByParams(Long approveSourceId, Integer approveSourceType, Integer approveStatus, Integer eventType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            builder.and().equal("approve_status", approveStatus, Types.INTEGER);
            builder.and().equal("event_type", eventType, Types.INTEGER);
            return tmsTransportApproveRepo.getDao().count(builder,hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countQueryApproveListV2(QueryApproveListDO approveListDO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            sqlStr.append("SELECT COUNT(*) FROM tms_transport_approve approve ");
            builder.setTemplate(getQueryApproveBaseSQL(approveListDO, sqlStr, 1, parameters, true));
            return tmsTransportApproveRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("countQueryApproveListV2 error", e);
        }
    }

    @Override
    public List<TmsTransportApprovePO> queryApproveListV2(QueryApproveListDO approveListDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<TmsTransportApprovePO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM tms_transport_approve approve ");
        StatementParameters parameters = new StatementParameters();
        builder.setTemplate(getQueryApproveBaseSQL(approveListDO, sqlStr, 1, parameters, false));
        List<TmsTransportApprovePO> transportApprovePOS;
        try {
            builder.mapWith(tmsTransportApprovePODalRowMapper);
            transportApprovePOS = tmsTransportApproveRepo.getQueryDao().query(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException("countQueryApproveListV2 error", e);
        }
        return transportApprovePOS;
    }

    @Override
    public int queryTemporaryDispatchApproveCount(Long approveSourceId, Integer approveSourceType, Integer eventType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_source_type", approveSourceType, Types.INTEGER);
            builder.and().equal("event_type", eventType, Types.INTEGER);
            return tmsTransportApproveRepo.getDao().count(builder,hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public void update(TmsTransportApprovePO po) {
        this.tmsTransportApproveRepo.update(po);
    }

    private String getQueryApproveBaseSQL(QueryApproveListDO approveListDO, StringBuilder sqlStr, int i, StatementParameters parameters, boolean isGetCount) {
        sqlStr.append(" WHERE approve.event_type = ? ");
        parameters.set(i++, "approve.event_type", Types.TINYINT, approveListDO.getEventType());
        if (!Strings.isNullOrEmpty(approveListDO.getApproveName())) {
            sqlStr.append(" AND approve.approve_name LIKE ? ");
            parameters.set(i++, "approve.approve_name", Types.VARCHAR, BaseUtil.getLikeSuffixSQLStr(approveListDO.getApproveName()));
        }
        if (!Strings.isNullOrEmpty(approveListDO.getVehLicense())) {
            sqlStr.append(" AND approve.approve_name LIKE ? ");
            parameters.set(i++, "approve.approve_name", Types.VARCHAR, BaseUtil.getLikeSuffixSQLStr(approveListDO.getVehLicense()));
        }
        if (approveListDO.getSupplierId() != null && approveListDO.getSupplierId() > 0) {
            sqlStr.append(" AND approve.supplier_id = ? ");
            parameters.set(i++, "approve.supplier_id", Types.BIGINT, approveListDO.getSupplierId());
        }
        if (approveListDO.getApproveStatus() != null) {
            sqlStr.append(" AND approve.approve_status = ? ");
            parameters.set(i++, "approve.approve_status", Types.INTEGER, approveListDO.getApproveStatus());
        }
        if (approveListDO.getApproveSourceId() != null) {
            sqlStr.append(" AND approve.approve_source_id = ? ");
            parameters.set(i++, "approve.approve_source_id", Types.BIGINT, approveListDO.getApproveSourceId());
        }
        if (approveListDO.getOneSelfStatus() != null) {
            String accountId = Strings.isNullOrEmpty(approveListDO.getAccountId()) ? "" : approveListDO.getAccountId();
            if (approveListDO.getOneSelfStatus() == 1) {
                sqlStr.append(" AND approve.account_id = ? ");
            } else {
                sqlStr.append(" AND approve.account_id != ? ");
            }
            parameters.set(i++, "approve.account_id", Types.VARCHAR, accountId);
        }
        if (CollectionUtils.isNotEmpty(approveListDO.getDrvIdList())) {
            sqlStr.append(" AND approve.approve_source_type = ? AND approve.approve_source_id IN ? ");
            parameters.set(i++, "approve.approve_source_type", Types.TINYINT, TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode());
            parameters.setInParameter(i++, "approve.approve_source_id", Types.BIGINT, approveListDO.getDrvIdList());
        }
        if (approveListDO.getVerifyStatus() != null) {
            sqlStr.append(" AND approve.approve_schedule = ? ");
            parameters.set(i++, "approve.approve_schedule", Types.TINYINT, approveListDO.getVerifyStatus());
        }
        if (!Strings.isNullOrEmpty(approveListDO.getCommitTimeStart())) {
            sqlStr.append(" AND approve.datachange_createtime >= ? ");
            parameters.set(i++, "approve.datachange_createtime", Types.TIMESTAMP, approveListDO.getCommitTimeStart());
        }
        if (!Strings.isNullOrEmpty(approveListDO.getCommitTimeEnd())) {
            sqlStr.append(" AND approve.datachange_createtime <= ? ");
            parameters.set(i++, "approve.datachange_createtime", Types.TIMESTAMP, approveListDO.getCommitTimeEnd());
        }
        if (approveListDO.getOcrheadPortraitResult() != null) {
            sqlStr.append(" AND approve.certificate_check_result = ? ");
            parameters.set(i++, "approve.certificate_check_result", Types.VARCHAR, approveListDO.getOcrheadPortraitResult() == 0 ? String.valueOf(Boolean.FALSE) : String.valueOf(Boolean.TRUE));
        }
        if (!isGetCount && approveListDO.getPageNo() != null && approveListDO.getPageSize() != null) {
            sqlStr.append(" ORDER BY approve.datachange_lasttime DESC LIMIT ");
            sqlStr.append((approveListDO.getPageNo() - 1) * approveListDO.getPageSize());
            sqlStr.append(",");
            sqlStr.append(approveListDO.getPageSize());
        }
        return sqlStr.toString();
    }

}
