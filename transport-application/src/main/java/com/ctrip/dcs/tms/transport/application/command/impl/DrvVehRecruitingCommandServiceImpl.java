package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.application.query.DriverPasswordService;
import com.ctrip.dcs.tms.transport.application.query.DrvVehRecruitingQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.application.dto.ComplianceVerificationResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.*;
import java.time.LocalDate;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.*;

/**
* 司机车辆审批类接口
* <AUTHOR>
* @date 2020/4/20 15:17
*/
@Service
public class DrvVehRecruitingCommandServiceImpl implements DrvVehRecruitingCommandService {

    private static final Logger logger = LoggerFactory.getLogger(DrvVehRecruitingCommandServiceImpl.class);

    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private TmsModRecordCommandService recordCommandService;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private RecruitingCommandService recruitingCommandService;
    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private DriverPasswordService driverPasswordService;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private DriverSafetyCommandService driverSafetyCommandService;
    @Autowired
    private CertificateCheckCommandService certificateCheckCommandService;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    DrvVehRecruitingQueryService drvVehRecruitingQueryService;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    TmsRecruitingApproveStepRepository stepRepository;
    @Autowired
    TmsRecruitingApproveStepChildRepository childRepository;

    @Autowired
    AuthorizationCheckService authorizationCheckService;
    @Autowired
    DriverQueryService driverQueryService;

    @Autowired
    MobileHelper mobileUtil;

    @Autowired
    OverageQConfig overageQConfig;

    @Autowired
    CommonConfig commonConfig;

    @Autowired
    private InternationalEntryService internationalEntryService;


    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> addDrvVehRecruiting(DrvVehRecruitingAddSOARequestType requestType) {
        try{
            // check mobile
            Result<Boolean> siMobileValid = mobileUtil.isMobileValid(requestType.getIgtCode(), requestType.getDrvPhone(),
              requestType.getCityId());
            if(!siMobileValid.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                  .withCode(siMobileValid.getCode())
                  .withMsg(siMobileValid.getMsg())
                  .withData(siMobileValid.getData())
                  .build();
            }

            //校验司机信息是否已存在
            Result<Boolean> checkResult = commonCheck(requestType.getDrvPhone(),requestType.getDrvIdcard(),requestType.getLoginAccount(),requestType.getInternalScope(),requestType.getEmail(), requestType.getSupplierId(), requestType.getDrvProLineList(), requestType.getVehProLineList());
            if(!checkResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                        .withCode(checkResult.getCode())
                        .withMsg(checkResult.getMsg())
                        .withData(checkResult.getData())
                        .build();
            }
            //新重构版本并且境内司机 校验
            if(requestType.getVersionFlag()!=null && Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getInternalScope())){
                //校验境内招募司机是否否存在手机号、身份证号
                Result<Boolean> checkRecruitingResult  = drvVehRecruitingQueryService.checkRecruitingDrvOnly(requestType.getDrvPhone(),requestType.getDrvIdcard());
                if(!checkRecruitingResult.isSuccess()){
                    return Result.Builder.<Boolean>newResult().fail().withCode(checkRecruitingResult.getCode()).withMsg(checkRecruitingResult.getMsg()).withData(checkRecruitingResult.getData()).build();
                }
                //校验境内招募车辆是否否存在车牌号、VIN码
                Result<Boolean> checkVehRecruitingResult  = drvVehRecruitingQueryService.checkRecruitingVehicleOnly(requestType.getVehicleLicense(),requestType.getVin());
                if(!checkVehRecruitingResult.isSuccess()){
                    return Result.Builder.<Boolean>newResult().fail().withCode(checkVehRecruitingResult.getCode()).withMsg(checkVehRecruitingResult.getMsg()).withData(checkVehRecruitingResult.getData()).build();
                }
                //是否存在vin码
                if(vehicleRepository.checkVehOnly(requestType.getVin(),TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())){
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists),requestType.getVin());
                }
            }else {
                //派安盈账户唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(requestType.getPaiayAccount()) && drvDrvierRepository.checkDrvOnly(requestType.getPaiayAccount(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)).build();
                }
                //派安盈邮箱唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(requestType.getPaiayEmail()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getPaiayEmail(), KeyType.Mail), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)).build();
                }
            }
            //判断登录密码是否合规
            if(StringUtils.isNotEmpty(requestType.getLoginPwd()) && !driverPasswordService.isPasswordValid(requestType.getLoginPwd())){
                return Result.Builder.<Boolean>newResult().fail().withCode("405").withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginPassworderror)).build();
            }
            long vehicleId = 0;
            long count = 0;
            requestType.setDrvFrom(TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode());
            requestType.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.NO.getCode());
            if(StringUtils.isNotEmpty(requestType.getVehicleLicense())){
                Boolean isVehLicense = vehicleRepository.isVehicleLicenseUniqueness(null,requestType.getVehicleLicense());
                //h5境外司机入注如果车辆入驻失败，则不拦截整体入注流程
                if(Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getInternalScope()) && !isVehLicense){
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists),requestType.getVehicleLicense());
                }
                if(isVehLicense){
                    // 判断入驻时间是否正确
                    Result<Boolean> booleanResult = checkRegisterTime(requestType);
                    if(!booleanResult.isSuccess()){
                        return Result.Builder.<Boolean>newResult().fail().withCode(booleanResult.getCode()).withMsg(booleanResult.getMsg()).withData(booleanResult.getData()).build();
                    }
                    vehicleId =  vehicleRecruitingRepository.addVehicleRecruiting(buildVehPO(requestType));
                }
            }
            if(vehicleId > 0){
                requestType.setVehicleId(vehicleId);
            }

            if(StringUtils.isNotEmpty(requestType.getDrvName()) && StringUtils.isNotEmpty(requestType.getDrvPhone())){
                count =  drvRecruitingRepository.addDrvRecruiting(buildDrvPO(requestType));
            }
            if(count > 0){
                Map<String,Object> nucleicAcidMap = toDoNucleicAcidLabelMap(count, requestType.getCityId(), requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getNucleicAcidTestingTime(), requestType.getOcrNucleicAcidTestingData());
                toDoVaccineLabelMap(count, requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getVaccinationTimeList(), requestType.getOcrVaccineData(),nucleicAcidMap);
                //生成新版单项
                if(requestType.getVersionFlag()!=null && requestType.getVersionFlag()>=3 && Objects.equals(requestType.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode())){
                    recruitingCommandService.initH5SingleApprovalData(count,vehicleId,requestType, requestType.getModifyUser(),nucleicAcidMap);
                }
                if (Objects.equals(requestType.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode()) && requestType.isOcrheadPortraitResult() != null) {
                    toDoOcrHeadPortraitLabel(count, requestType.ocrheadPortraitResult);
                    toDoComplianceHeadPortraitLabel(count);
                }
                //添加操作记录
                recordCommandService.drvVehRecruitingInsertModRrd(DrvVehRecruitingInsertModRrdDTO.buildDTO(count,TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode(),"",requestType.getModifyUser(), TmsTransportConstant.RecruitingTypeEnum.drv.getCode()));
                //如果H5是境外数据&&是新业务VersionFlag=5并且OCR识别通过,则走新逻辑
                Result<Boolean> overseasResult = overseasIsBusiness(requestType,count,vehicleId);
                if(!overseasResult.isSuccess()){
                    return Result.Builder.<Boolean>newResult().fail().withCode(overseasResult.getCode()).withMsg(overseasResult.getMsg()).build();
                }
                //发送审批时效qmq
                qmqProducerCommandService.sendRecruitingApproveAgingQMQ(count,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode());
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }




    private Result<Boolean> checkRegisterTime(DrvVehRecruitingAddSOARequestType requestType) {
        String regstDate = requestType.getRegstDate();
        Long cityId = requestType.getCityId();
        if (BooleanUtils.isFalse(commonConfig.getOverageGraySwitch())  || (BooleanUtils.isTrue(commonConfig.getOverageGraySwitch()) && CollectionUtils.isNotEmpty(commonConfig.getCityIdList()) && commonConfig.getCityIdList().contains(cityId))) {
            Long vehicleTypeId = requestType.getVehicleTypeId();
            City city = enumRepository.getCityById(cityId);
            if (city.isChineseMainland()) {
                LocalDate now = LocalDate.now();
                LocalDate registerDate = now;
                if (StringUtils.isNoneBlank(regstDate)) {
                    registerDate = DateUtil.convertStringToDate(regstDate, DateUtil.YYYYMMDD);
                }
                if (registerDate.isAfter(now)) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
                }
                OverageDTO overageMap = overageQConfig.getOverageMap(cityId, vehicleTypeId);
                Double accessLimit = overageMap.getAccessLimit();
                BigDecimal accessMonth = BigDecimal.valueOf(accessLimit).multiply(BigDecimal.valueOf(12L));
                LocalDate overAgeDate = now.minusMonths(accessMonth.intValue());
                logger.info("checkRegisterTime", " licene :{}registerDate: {} overAgeDate :{}", requestType.getVehicleLicense(), registerDate.toString(), overAgeDate.toString());
                if (registerDate.isBefore(overAgeDate)) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
                }
            }
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }


    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateDrvVehRecruiting(DrvVehRecruitingUpdateSOARequestType requestType) {
        try{

            Integer recruitingType = requestType.getRecruitingType();
            //招募类型必传,并且只能为1or2
            if(recruitingType == null){
                recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
            }
            //判断单项是否已审批完成
            //todo 现在自动合规
            if(judgeSingleStatus(requestType.getSingleList())){
                return Result.Builder.<Boolean>newResult().fail().withCode("602").build();
            }
            //招募详情兼容车辆招募,为了区分司机和车辆，查询条件中固定RecruitingType 便于查询
            if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                return this.updateVehicleRecruiting(requestType);
            }
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(requestType.getDrvRecruitingId());
            if(drvRecruitingPO == null){
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDrvVehRecruitingDoesNotExist),String.valueOf(requestType.getDrvRecruitingId()));
            }
            if(!drvRecruitingPO.getActive()){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).withData(false).build();
            }
            Integer versionFlag = drvRecruitingPO.getVersionFlag() == null?0:drvRecruitingPO.getVersionFlag();
            //同一个角色下，不能重复审批
            if(checkNowRoleApproveStatus(drvRecruitingPO.getApproverStatus(),drvRecruitingPO.getCityId(),versionFlag)){
                return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
            }
            //如果身份证号变更，驾驶证单项审批，则要判断身份证单项是否待审批
            if(checkDrvIdCardStepStatus(drvRecruitingPO.getDrvRecruitingId(),requestType.getDrvIdcard(),drvRecruitingPO.getDrvIdcard(),requestType.getSingleList())){
                return Result.Builder.<Boolean>newResult().fail().withCode("602").withData(false).build();
            }
            //校验司机信息是否已存在
            Result<Boolean> checkResult =  commonUpdateCheck(requestType,drvRecruitingPO);
            if(!checkResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                        .withCode(checkResult.getCode())
                        .withMsg(checkResult.getMsg())
                        .withData(checkResult.getData())
                        .build();
            }

            //新增招募编辑审批唯一性
            Result<Boolean> recruitingCheckResult = recruitingUpdateCheck(requestType,drvRecruitingPO);
            if(!recruitingCheckResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                        .withCode(recruitingCheckResult.getCode())
                        .withMsg(recruitingCheckResult.getMsg())
                        .withData(recruitingCheckResult.getData())
                        .build();
            }
            Long vehicleId = drvRecruitingPO.getVehicleId();
            long count = 0;
            //自助司机,可编辑车辆信息
            if(Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode()) && vehicleId!=null && vehicleId > 0){
                VehicleRecruitingPO vehicleRecruitingPO =  vehicleRecruitingRepository.queryByPK(vehicleId);
                if(vehicleRecruitingPO == null) {
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehApprovalInfoNotExist),String.valueOf(vehicleId));
                }
                //校验车辆信息是否已存在于招募车辆中
                Result<Boolean> vehCheckResult = this.commonVehCheck(requestType,vehicleRecruitingPO);
                if(!vehCheckResult.isSuccess()){
                    return Result.Builder.<Boolean>newResult().fail()
                            .withCode(vehCheckResult.getCode())
                            .withMsg(vehCheckResult.getMsg())
                            .withData(vehCheckResult.getData())
                            .build();
                }
                VehicleRecruitingPO updateVehPO = buildUpdateVehPO(requestType);
                updateVehPO.setModSnapshotValues(getVehModSnapshotValues(vehicleRecruitingPO.getModSnapshotValues(),vehicleRecruitingPO.getVehicleLicense(),vehicleRecruitingPO.getVin(),requestType.getSingleList()));
                updateVehPO.setOcrPassStatusJson(ocrPassJsonToString(requestType.getOcrPassStatusList(),SingleApproveTypeEnum.VEH));
                vehicleRecruitingRepository.update(updateVehPO);
            }
            DrvRecruitingPO ready2Update = buildUpdateDrvPO(requestType);
            ready2Update.setOcrVaccineData(drvRecruitingPO.getOcrVaccineData());
            if (!Strings.isNullOrEmpty(requestType.getOcrVaccineData())) {
                ready2Update.setOcrVaccineData(requestType.getOcrVaccineData());
            }
            ready2Update.setOcrNucleicAcidData(drvRecruitingPO.getOcrNucleicAcidData());
            if (!Strings.isNullOrEmpty(requestType.getOcrNucleicAcidTestingData())) {
                ready2Update.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
            }
            ready2Update.setModSnapshotValues(getDrvModSnapshotValues(drvRecruitingPO.getModSnapshotValues(),drvRecruitingPO.getDrvIdcard(),drvRecruitingPO.getDrvName(),requestType.getSingleList()));
            ready2Update.setOcrPassStatusJson(ocrPassJsonToString(requestType.getOcrPassStatusList(),SingleApproveTypeEnum.DRV));
            count =  drvRecruitingRepository.update(ready2Update);

            if(count > 0) {
                //fixme 供应商编辑核验、疫苗,同步子标签
                Map<String,Object> nucleicAcidMap =  toDoNucleicAcidLabelMap(requestType.getDrvRecruitingId(), requestType.getCityId(), requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getNucleicAcidTestingTime(), requestType.getOcrNucleicAcidTestingData());
                toDoVaccineLabelMap(requestType.getDrvRecruitingId(), requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getVaccinationTimeList(), requestType.getOcrVaccineData(),nucleicAcidMap);
                //核酸疫苗状态同步子标签
                syncNucleicVaccineChild(nucleicAcidMap,requestType.getDrvRecruitingId(),drvRecruitingPO.getVersionFlag());
                //fixme 单项审批
                singleApproval(drvRecruitingPO.getVersionFlag(),requestType);
                if (Objects.equals(requestType.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode()) && requestType.isOcrheadPortraitResult() != null) {
                    if (!Objects.equals(requestType.getDrvHeadImg(), drvRecruitingPO.getDrvHeadImg())) {
                        toDoComplianceHeadPortraitLabel(requestType.getDrvRecruitingId());
                        toDoOcrHeadPortraitLabel(requestType.getDrvRecruitingId(), requestType.isOcrheadPortraitResult());
                    }
                }
                //修改证件状态
                if(CollectionUtils.isNotEmpty(requestType.getCheckStatusList())){
                    for(UpdateCertificateStatusSOADTO statusSOADTO : requestType.getCheckStatusList()){
                        TmsCertificateCheckPO checkPO =  checkRepository.queryByPK(statusSOADTO.getId());
                        if(!Objects.isNull(checkPO)){
                            Integer orgCheckStatus = checkPO.getCheckStatus();
                            checkRepository.updateCheckStatus(statusSOADTO.getId(),statusSOADTO.getCheckStatus());
                            recordCommandService.drvVehRecruitingInsertModRrdList(Arrays.asList(requestType.getDrvRecruitingId()), CommonEnum.RecordTypeEnum.CHECK,statusSOADTO.getCheckStatus(),requestType.getModifyUser(),orgCheckStatus,checkPO.getCertificateType());
                        }
                    }
                }
                //添加招募司机，直接通过
                if(requestType.getAction()!=null){
                    recruitingCommandService.approveRoute(buildRequest(requestType), TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
                }

                //工作台创建的司机招募&并且换车
                if (Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode()) && requestType.getVehicleId() != null && !Objects.equals(drvRecruitingPO.getVehicleId(), requestType.getVehicleId())) {
                    VehVehiclePO vehVehiclePo = vehicleRepository.queryByPk(requestType.getVehicleId());
                    if (vehVehiclePo != null) {
                        Result<String> bindLimitResult;
                        if (BooleanUtils.isTrue(qconfig.getCheckBindFlag())){
                            bindLimitResult = productionLineUtil.checkBind(drvRecruitingPO.getDrvId(), drvRecruitingPO.getCategorySynthesizeCode(), vehVehiclePo.getCategorySynthesizeCode());
                        }else {
                            bindLimitResult = productionLineUtil.bindTransportCheck(drvRecruitingPO.getCategorySynthesizeCode(), vehVehiclePo.getCategorySynthesizeCode());
                        }
                        if (!bindLimitResult.isSuccess()) {
                            return Result.Builder.<Boolean>newResult().success().withData(true).withMsg(bindLimitResult.getMsg()).build();
                        }
                    }
                }

                //编辑招募信息，如果当前供应商在灰度中，并且是境外新逻辑，则判断OCR是否通过
                Result<Boolean> overseasResult = overseasRecruitingAuto(requestType, drvRecruitingPO.getVersionFlag(),drvRecruitingPO.getVersionFlag(), drvRecruitingPO.getDrvRecruitingId(), vehicleId, RecruitingTypeEnum.drv, SingleApproveTypeEnum.DRV);
                if (!overseasResult.isSuccess()) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(overseasResult.getCode()).withMsg(overseasResult.getMsg()).build();
                }
                //司机核验状态
                qmqProducerCommandService.sendRecruitingCheckStatusQMQ(drvRecruitingPO.getDrvRecruitingId(),recruitingType,requestType.getModifyUser());
                //单项不审批处理
                sigleApproveNoPass(buildWorkStepApproveNoPassVO(requestType, drvRecruitingPO.getDrvFrom(),vehicleId));
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }


    @Override
    public void toDoNucleicAcidLabel(Long drvRecruitingId, Long cityId, String name, String idNumber, String nucleicAcidTestingTime, String ocrData) {
        if (Strings.isNullOrEmpty(nucleicAcidTestingTime) || Strings.isNullOrEmpty(ocrData)) {
            return;
        }
        SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(ocrData, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
        });
        idNumber = TmsTransUtil.decrypt(idNumber, KeyType.Identity_Card);
        Result<EpidemicPreventionControlEnum.ReportResultEnum> sameCheck = driverSafetyCommandService.baseVerifySameDriver(name, idNumber, ocrRequest.getDriverName(), ocrRequest.getDriverIdNumber());
        if (sameCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            return;
        }
        Result<EpidemicPreventionControlEnum.ReportResultEnum> resultCheck = driverSafetyCommandService.checkNucleicAcidTestingReport(cityId, DateUtil.parseDate(nucleicAcidTestingTime), ocrRequest.getNucleicAcidTestingResult(), DateUtil.parseDate(ocrRequest.getNucleicAcidTestingTime()), ocrRequest.getNucleicAcidTestingResult());
        if (resultCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            return;
        }
        addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
    }


    @Override
    public void toDoVaccineLabel(Long drvRecruitingId, String name, String idNumber, List<String> vaccinationTimeList, String ocrData) {
        if (CollectionUtils.isEmpty(vaccinationTimeList) || Strings.isNullOrEmpty(ocrData)) {
            return;
        }
        SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(ocrData, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
        });
        idNumber = TmsTransUtil.decrypt(idNumber, KeyType.Identity_Card);
        Result<EpidemicPreventionControlEnum.ReportResultEnum> sameCheck = driverSafetyCommandService.baseVerifySameDriver(name, idNumber, ocrRequest.getDriverName(), ocrRequest.getDriverIdNumber());
        if (sameCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            return;
        }
        if (Strings.isNullOrEmpty(ocrRequest.getVaccineName()) || ocrRequest.getVaccinationCount() == null || !Objects.equals(vaccinationTimeList.size(), ocrRequest.getVaccinationCount())) {
            addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            return;
        }
        for (int i = 0; i < vaccinationTimeList.size(); i++) {
            if (i == 0) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getFirstVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    return;
                }
            }
            if (i == 1) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getSecondVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    return;
                }
            }
            if (i == 2) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getThirdVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    return;
                }
            }
        }

        Long nowDate = (new java.util.Date()).getTime();
        for (String vaccinationTime : vaccinationTimeList) {
            java.util.Date vaccinationDate = DateUtil.stringToDate(vaccinationTime, DateUtil.YYYYMMDD);
            if (vaccinationDate == null || vaccinationDate.getTime() > nowDate) {
                addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                return;
            }
        }
        addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
    }

    @Override
    public void toDoOcrHeadPortraitLabel(Long drvRecruitingId, boolean checkResult) {
        certificateCheckCommandService.insertCheckRecordDB(drvRecruitingId,
                TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                TmsTransportConstant.CertificateTypeEnum.OCR_HEAD_PORTRAIT.getCode()
                , "", "", Lists.newArrayList(), checkResult ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(),checkResult ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(), Constant.SYSTEM);
    }

    @Override
    public void toDoComplianceHeadPortraitLabel(Long drvRecruitingId) {
        certificateCheckCommandService.insertCheckRecordDB(drvRecruitingId,
                TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode()
                , "", "", Lists.newArrayList(), TmsTransportConstant.CheckStatusEnum.REVIEW.getCode(),TmsTransportConstant.CheckStatusEnum.REVIEW.getCode(), Constant.SYSTEM);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateDrvVehRecruitingFromH5(DrvVehRecruitingUpdateFromH5RequestType requestType) {
        try{
            String requestDrvPhone  = requestType.getDrvPhone();
            //鉴权-当前手机号和验证码是否有效
            if(!drvVehRecruitingQueryService.h5VerificationPermissions(requestDrvPhone,requestType.getVerificationCode())){
                return Result.Builder.<Boolean>newResult().fail().withData(null).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtCurrentoperationNopermit)).build();
            }
            //H5司机只能编辑自己注册的信息，无权限查询工作台创建司机,新版逻辑
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(requestType.getDrvRecruitingId());
            if(drvRecruitingPO == null || !Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())||
            drvRecruitingPO.getVersionFlag() < 3){
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDrvVehRecruitingDoesNotExist),String.valueOf(requestType.getDrvRecruitingId()));
            }
            //境外司机暂不可操作
            if(!Objects.equals(drvRecruitingPO.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtOverseasdriverCannotoperat)).build();
            }

            //判断当前手机号是否有权限访问
            if (!Objects.equals(drvRecruitingPO.getDrvPhone(), TmsTransUtil.encrypt(requestDrvPhone, KeyType.Phone))) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtPhonenumberNotmatch)).build();
            }
            Long vehicleId = drvRecruitingPO.getVehicleId();
            if(vehicleId == null ||vehicleId <= 0){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVehicleNotempty)).build();
            }
            requestType.setModifyUser(drvRecruitingPO.getDrvName());
            //自助司机,可编辑车辆信息
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleId);
            if(vehicleRecruitingPO == null) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehApprovalInfoNotExist),String.valueOf(vehicleId));
            }

            //司机身份证、车牌号、VIN唯一校验
            Result<Boolean> recruitingCheckResult = recruitingUpdateCheckFromH5(requestType,drvRecruitingPO,vehicleRecruitingPO);
            if(!recruitingCheckResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                        .withCode(recruitingCheckResult.getCode())
                        .withMsg(recruitingCheckResult.getMsg())
                        .withData(recruitingCheckResult.getData())
                        .build();
            }
            vehicleRecruitingRepository.update(buildH5UpdateVehPO(requestType));
            DrvRecruitingPO ready2Update = buildH5UpdateDrvPO(requestType);
            ready2Update.setOcrVaccineData(drvRecruitingPO.getOcrVaccineData());
            if (!Strings.isNullOrEmpty(requestType.getOcrVaccineData())) {
                ready2Update.setOcrVaccineData(requestType.getOcrVaccineData());
            }
            ready2Update.setOcrNucleicAcidData(drvRecruitingPO.getOcrNucleicAcidData());
            if (!Strings.isNullOrEmpty(requestType.getOcrNucleicAcidTestingData())) {
                ready2Update.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
            }
            int count =  drvRecruitingRepository.update(ready2Update);

            if(count > 0) {
                //fixme 供应商编辑核验、疫苗,同步子标签
                Map<String,Object> nucleicAcidMap =  toDoNucleicAcidLabelMap(requestType.getDrvRecruitingId(), requestType.getCityId(), requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getNucleicAcidTestingTime(), requestType.getOcrNucleicAcidTestingData());
                toDoVaccineLabelMap(requestType.getDrvRecruitingId(), requestType.getDrvName(), requestType.getDrvIdcard(), requestType.getVaccinationTimeList(), requestType.getOcrVaccineData(),nucleicAcidMap);
                //核酸疫苗状态同步子标签
                syncNucleicVaccineChild(nucleicAcidMap,requestType.getDrvRecruitingId(),drvRecruitingPO.getVersionFlag());
                if (Objects.equals(requestType.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode()) && requestType.isOcrheadPortraitResult() != null) {
                    if (!Objects.equals(requestType.getDrvHeadImg(), drvRecruitingPO.getDrvHeadImg())) {
                        toDoComplianceHeadPortraitLabel(requestType.getDrvRecruitingId());
                        toDoOcrHeadPortraitLabel(requestType.getDrvRecruitingId(), requestType.isOcrheadPortraitResult());
                    }
                }
                //H5编辑提交到供应商审批
                recruitingCommandService.approveRoute(buildH5ApproveRequest(requestType), TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
                //单项不审批处理
                requestType.setRecruitingType(TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
                sigleApproveNoPass(buildH5StepApproveNoPassVO(requestType, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode(),vehicleId));
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            return Result.Builder.<Boolean>newResult().fail().withData(false).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> toDoNucleicAcidLabelMap(Long drvRecruitingId, Long cityId, String name, String idNumber, String nucleicAcidTestingTime, String ocrData) {
        Map<String, Object> resultMap = Maps.newHashMap();
        if (Strings.isNullOrEmpty(nucleicAcidTestingTime) || Strings.isNullOrEmpty(ocrData)) {
            return resultMap;
        }
        resultMap.put(NUCLEICACIDVACCINE_ITEM + TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode(),TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
        SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(ocrData, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
        });
        idNumber = TmsTransUtil.decrypt(idNumber, KeyType.Identity_Card);
        Result<EpidemicPreventionControlEnum.ReportResultEnum> sameCheck = driverSafetyCommandService.baseVerifySameDriver(name, idNumber, ocrRequest.getDriverName(), ocrRequest.getDriverIdNumber());
        if (sameCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            resultMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode(), SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtNucleicacidnameNotsame));
            return resultMap;
        }
        Result<EpidemicPreventionControlEnum.ReportResultEnum> resultCheck = driverSafetyCommandService.checkNucleicAcidTestingReport(cityId, DateUtil.parseDate(nucleicAcidTestingTime), ocrRequest.getNucleicAcidTestingResult(), DateUtil.parseDate(ocrRequest.getNucleicAcidTestingTime()), ocrRequest.getNucleicAcidTestingResult());
        if (resultCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            resultMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtNucleicacidAbnormal));
            return resultMap;
        }
        addCertificateCheck(true, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        resultMap.put(NUCLEICACIDVACCINE_ITEM + TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode(),TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        return resultMap;
    }

    @Override
    public Map<String, Object> toDoVaccineLabelMap(Long drvRecruitingId, String name, String idNumber, List<String> vaccinationTimeList, String ocrData,Map<String,Object> nucleicAcidMap) {
        if (CollectionUtils.isEmpty(vaccinationTimeList) || Strings.isNullOrEmpty(ocrData)) {
            return nucleicAcidMap;
        }
        SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(ocrData, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
        });
        nucleicAcidMap.put(NUCLEICACIDVACCINE_ITEM + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
        idNumber = TmsTransUtil.decrypt(idNumber, KeyType.Identity_Card);
        Result<EpidemicPreventionControlEnum.ReportResultEnum> sameCheck = driverSafetyCommandService.baseVerifySameDriver(name, idNumber, ocrRequest.getDriverName(), ocrRequest.getDriverIdNumber());
        if (sameCheck.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(), SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccinenameNotsame));
        }
        if (Strings.isNullOrEmpty(ocrRequest.getVaccineName()) || ocrRequest.getVaccinationCount() == null || !Objects.equals(vaccinationTimeList.size(), ocrRequest.getVaccinationCount())) {
            addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccineInfoerror));
            return nucleicAcidMap;
        }
        for (int i = 0; i < vaccinationTimeList.size(); i++) {
            if (i == 0) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getFirstVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccinefirsttimeNotsame));
                    return nucleicAcidMap;
                }
            }
            if (i == 1) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getSecondVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccinesecondtimeNotsame));
                    return nucleicAcidMap;
                }
            }
            if (i == 2) {
                if (!Objects.equals(vaccinationTimeList.get(i), ocrRequest.getThirdVaccinationTime())) {
                    addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                    nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccinethirdtimeNotsame));
                    return nucleicAcidMap;
                }
            }
        }

        Long nowDate = (new java.util.Date()).getTime();
        for (String vaccinationTime : vaccinationTimeList) {
            java.util.Date vaccinationDate = DateUtil.stringToDate(vaccinationTime, DateUtil.YYYYMMDD);
            if (vaccinationDate == null || vaccinationDate.getTime() > nowDate) {
                addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
                nucleicAcidMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtVaccinetimeEarlier));
                return nucleicAcidMap;
            }
        }
        addCertificateCheck(false, drvRecruitingId, ocrData, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        nucleicAcidMap.put(NUCLEICACIDVACCINE_ITEM + TmsTransportConstant.ApproveItemEnum.vaccine.getCode(),TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        return nucleicAcidMap;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> discardRecruitingDrv(Long recruitingId, List<String> permissionCodes, String modifyUser) {
        try {
            //权限判断
            Result<Boolean> authResult = authorizationCheckService.operationAuthJudge(permissionCodes,Constant.recruitingDiscardCode);
            if(!authResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail().withCode(authResult.getCode()).withMsg(authResult.getMsg()).build();
            }
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingId);
            if(Objects.isNull(drvRecruitingPO)){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
            }

            //境外数据不处理
            if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),drvRecruitingPO.getInternalScope())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWronAbroad)).build();
            }
            //审批通过的数据去正式司机处理
            if(Objects.equals(drvRecruitingPO.getApproverStatus(),TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.rectutinglistpageCardiscardtipWrong)).build();
            }
            int count = drvRecruitingRepository.discardRecruitingDrv(recruitingId,Boolean.FALSE,modifyUser);
            if(count > 0){
                //H5入注的司机，同步将车辆置为废弃
                if(Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                    vehicleRecruitingRepository.discardRecruitingVeh(drvRecruitingPO.getVehicleId(),Boolean.FALSE,modifyUser);
                }
                //插入变更记录
                recordCommandService.insertRecruitingActiveRrd(recruitingId, TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),modifyUser);
            }
            return Result.Builder.<Boolean>newResult().success().build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> discardRecruitingVeh(Long recruitingId, List<String> permissionCodes, String modifyUser) {
        try {
            //权限判断
            Result<Boolean> authResult = authorizationCheckService.operationAuthJudge(permissionCodes,Constant.recruitingDiscardCode);
            if(!authResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail().withCode(authResult.getCode()).withMsg(authResult.getMsg()).build();
            }

            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingId);
            if(Objects.isNull(vehicleRecruitingPO)){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
            }

            //境外数据不处理
            if(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()) == 1){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWronAbroad)).build();
            }

            //审批通过的数据去正式车辆处理
            if(Objects.equals(vehicleRecruitingPO.getApproverStatus(),TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.rectutinglistpageCardiscardtipWrong)).build();
            }

            //h5的车辆和h5司机是一体,要通过车辆iD获取司机ID，跳转到司机方法处理
            if(Objects.equals(vehicleRecruitingPO.getVehicleFrom(), TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())){
                List<DrvRecruitingPO> drvRecruitingPOList = drvRecruitingRepository.queryvRecruitingByVehicleIds(Arrays.asList(vehicleRecruitingPO.getVehicleId()));
                if(CollectionUtils.isEmpty(drvRecruitingPOList)){
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
                }
                DrvRecruitingPO drvRecruitingPO = drvRecruitingPOList.get(0);
                //特殊情况，如果h5的车辆，司机是审批通过，车辆不是审批通过，则只处理车辆数据
                if(Objects.equals(drvRecruitingPO.getApproverStatus(),TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode())){
                    this.updateRecruitingVeh(recruitingId,drvRecruitingPO.getDrvRecruitingId(),RecruitingTypeEnum.drv,modifyUser);
                    return Result.Builder.<Boolean>newResult().success().build();
                }
                return discardRecruitingDrv(drvRecruitingPO.getDrvRecruitingId(),permissionCodes,modifyUser);
            }
            //工作台车辆，直接处理
            if(Objects.equals(vehicleRecruitingPO.getVehicleFrom(), TmsTransportConstant.VehicleFromEnum.Veh_MANUAL.getCode())){
                this.updateRecruitingVeh(recruitingId,recruitingId,RecruitingTypeEnum.vehicle,modifyUser);
            }
            return Result.Builder.<Boolean>newResult().success().build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean updateRecruitingVeh(Long recruitingId,Long drvRecruitingId,RecruitingTypeEnum recruitingTypeEnum,String modifyUser){
        try {
            int count = vehicleRecruitingRepository.discardRecruitingVeh(recruitingId, Boolean.FALSE, modifyUser);
            if (count > 0) {
                //插入变更记录
                recordCommandService.insertRecruitingActiveRrd(drvRecruitingId, recruitingTypeEnum.getCode(), modifyUser);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    private void addCertificateCheck(Boolean isNucleicAcidDetectionType, Long drvRecruitingId, String ocrData, Integer checkStatus) {
        CertificateCheckSOAInfo checkSOAInfo = new CertificateCheckSOAInfo();
        checkSOAInfo.setColumnKey(isNucleicAcidDetectionType ? SharkKeyConstant.WORKBENCH_NUCLEIC_ACID_REPORT_RETURN_INFO : SharkKeyConstant.WORKBENCH_VACCINE_REPORT_RETURN_INFO);
        checkSOAInfo.setColumnValue(SharkUtils.getSharkValue(checkSOAInfo.getColumnKey()));
        certificateCheckCommandService.insertCheckRecordDB(drvRecruitingId,
                TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                isNucleicAcidDetectionType ? TmsTransportConstant.CertificateTypeEnum.NUCLEIC_ACID.getCode() : TmsTransportConstant.CertificateTypeEnum.VACCINE.getCode()
                , "", ocrData, Lists.newArrayList(checkSOAInfo), checkStatus,checkStatus, Constant.SYSTEM);
    }

    /**
     * 车辆招募编辑
     * @param requestType
     * @return
     */
    private Result<Boolean> updateVehicleRecruiting(DrvVehRecruitingUpdateSOARequestType requestType){
        try {
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(requestType.getDrvRecruitingId());
            if(vehicleRecruitingPO == null){
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDrvVehRecruitingDoesNotExist),String.valueOf(requestType.getDrvRecruitingId()));
            }
            if(!vehicleRecruitingPO.getActive()){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).withData(false).build();
            }
            Integer versionFlag = vehicleRecruitingPO.getVersionFlag() == null?0:vehicleRecruitingPO.getVersionFlag();
            //同一个角色下，不能重复审批
            if(checkNowRoleApproveStatus(vehicleRecruitingPO.getApproverStatus(),vehicleRecruitingPO.getCityId(),versionFlag)){
                return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
            }
            //校验车辆信息是否已存在
            Result<Boolean> checkResult = this.commonVehCheck(requestType,vehicleRecruitingPO);
            if(!checkResult.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail()
                        .withCode(checkResult.getCode())
                        .withMsg(checkResult.getMsg())
                        .withData(checkResult.getData())
                        .build();
            }
            VehicleRecruitingPO updateVehiclePO = buildUpdateVehicleRePO(requestType);
            updateVehiclePO.setModSnapshotValues(getVehModSnapshotValues(vehicleRecruitingPO.getModSnapshotValues(),vehicleRecruitingPO.getVehicleLicense(),vehicleRecruitingPO.getVin(),requestType.getSingleList()));
            updateVehiclePO.setOcrPassStatusJson(ocrPassJsonToString(requestType.getOcrPassStatusList(),SingleApproveTypeEnum.VEH));
            int count = vehicleRecruitingRepository.update(updateVehiclePO);
            if(count > 0){
                //fixme 单项审批
                singleApproval(vehicleRecruitingPO.getVersionFlag(),requestType);
                //添加招募司机，直接通过
                if(requestType.getAction()!=null){
                    recruitingCommandService.approveRoute(buildRequest(requestType),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode());
                }
                //修改证件状态
                if(CollectionUtils.isNotEmpty(requestType.getCheckStatusList())){
                    for(UpdateCertificateStatusSOADTO statusSOADTO : requestType.getCheckStatusList()){
                        TmsCertificateCheckPO checkPO =  checkRepository.queryByPK(statusSOADTO.getId());
                        if(!Objects.isNull(checkPO)){
                            Integer orgCheckStatus = checkPO.getCheckStatus();
                            checkRepository.updateCheckStatus(statusSOADTO.getId(),statusSOADTO.getCheckStatus());
                            recordCommandService.drvVehRecruitingInsertModRrdList(Arrays.asList(requestType.getDrvRecruitingId()), CommonEnum.RecordTypeEnum.VEH_CHECK,statusSOADTO.getCheckStatus(),requestType.getModifyUser(),orgCheckStatus,checkPO.getCertificateType());
                        }
                    }
                }

                //编辑招募信息，如果当前供应商在灰度中，并且是境外新逻辑，则判断OCR是否通过
                Result<Boolean> overseasResult = overseasRecruitingAuto(requestType, vehicleRecruitingPO.getVersionFlag(), enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()), vehicleRecruitingPO.getVehicleId(), null, RecruitingTypeEnum.vehicle, SingleApproveTypeEnum.VEH);
                if (!overseasResult.isSuccess()) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(overseasResult.getCode()).withMsg(overseasResult.getMsg()).build();
                }
                //司机核验状态
                qmqProducerCommandService.sendRecruitingCheckStatusQMQ(requestType.getDrvRecruitingId(),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),requestType.getModifyUser());
                //添加操作记录
//                recordCommandService.drvVehRecruitingInsertModRrd(DrvVehRecruitingInsertModRrdDTO.buildDTO(vehicleRecruitingPO.getVehicleId(),vehicleRecruitingPO.getApproverStatus(),requestType.getRemark(),requestType.getModifyUser(),requestType.getRecruitingType()));
                //单项不审批处理
                sigleApproveNoPass(buildWorkStepApproveNoPassVO(requestType, null,requestType.getDrvRecruitingId()));
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private VehicleRecruitingPO buildUpdateVehicleRePO(DrvVehRecruitingUpdateSOARequestType requestType){
        VehicleRecruitingPO  vehicleRecruitingPO = new VehicleRecruitingPO();
        BeanUtils.copyProperties(requestType,vehicleRecruitingPO);
        vehicleRecruitingPO.setVehicleId(requestType.getDrvRecruitingId());
        vehicleRecruitingPO.setCityId(requestType.getVehicleCityId());
        if(StringUtils.isNotEmpty(requestType.getRegstDate())){
            vehicleRecruitingPO.setRegstDate(Timestamp.valueOf(requestType.getRegstDate()+" 00:00:00"));
        }
        vehicleRecruitingPO.setNetAppealMaterials(requestType.getVehNetAppealMaterials());
        return vehicleRecruitingPO;
    }

    private VehicleRecruitingPO buildVehPO(DrvVehRecruitingAddSOARequestType requestType){
        VehicleRecruitingPO vehPO = new VehicleRecruitingPO();
        BeanUtils.copyProperties(requestType,vehPO);
        if(StringUtils.isNotEmpty(requestType.getRegstDate())){
            vehPO.setRegstDate(Timestamp.valueOf(requestType.getRegstDate()+" 00:00:00"));
        }
        vehPO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(requestType.getVehProLineList()));
        vehPO.setVersionFlag(requestType.getVersionFlag() == null?qconfig.getSystemVersionFlag():requestType.getVersionFlag());
        vehPO.setApproveSchedule(0);
        vehPO.setSupplierApproveSchedule(0);
        Map<String,String> modSnapshotMap = Maps.newHashMap();
        modSnapshotMap.put("vehicleLicense",vehPO.getVehicleLicense());
        modSnapshotMap.put("vin",vehPO.getVin());
        vehPO.setModSnapshotValues(JsonUtil.toJson(modSnapshotMap));
        vehPO.setOcrFieldValue(requestType.getVehOcrFieldValue());
        vehPO.setNewOcrFieldValue(requestType.getNewVehOcrFieldValue());
        vehPO.setOcrPassStatusJson(ocrPassJsonToString(requestType.getOcrPassStatusList(),SingleApproveTypeEnum.VEH));
        OcrComplianceDTO complianceRuleGary = internationalEntryService.isInComplianceRuleGary(requestType.getCityId());
        if (Objects.nonNull(complianceRuleGary)) {
            ComplianceVerificationResultDTO complianceResult = internationalEntryService.complianceVerification(complianceRuleGary.getComplianceType(), requestType.getVehicleLicense(), requestType.getNewOcrFieldValue());
            vehPO.setAuditStatus(Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1);
            OcrPassStatusModelSOA ocrPassStatusModelSOA = new OcrPassStatusModelSOA();
            ocrPassStatusModelSOA.setOcrId(0L);
            ocrPassStatusModelSOA.setOcrItem(ApproveItemEnum.vehicle_compliance.getCode());
            ocrPassStatusModelSOA.setPassStatus(BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1)));
            requestType.getOcrPassStatusList().add(ocrPassStatusModelSOA);
        }
        return vehPO;
    }

    public DrvRecruitingPO buildDrvPO(DrvVehRecruitingAddSOARequestType requestType){
        DrvRecruitingPO drvPO = new DrvRecruitingPO();
        BeanUtils.copyProperties(requestType,drvPO);
        drvPO.setOcrVaccineData(requestType.getOcrVaccineData());
        drvPO.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
        if(StringUtils.isNotEmpty(requestType.getExpiryBeginDate())){
            drvPO.setExpiryBeginDate(Date.valueOf(requestType.getExpiryBeginDate()));
        }
        if(StringUtils.isNotEmpty(requestType.getExpiryEndDate())){
            drvPO.setExpiryEndDate(Date.valueOf(requestType.getExpiryEndDate()));
        }
        drvPO.setDrvPhone(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone));
        drvPO.setEmail(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail));
        drvPO.setDrvIdcard(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card));
        drvPO.setDrvLicenseNumber(TmsTransUtil.encrypt(requestType.getDrvLicenseNumber(), KeyType.Identity_Card));
        if(Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(),requestType.getInternalScope())){
            if(StringUtils.isNotEmpty(requestType.getCertiDate())){
                drvPO.setCertiDate(Date.valueOf(requestType.getCertiDate()));
            }
        }else{
            //生成密码
            if (StringUtils.isNotEmpty(requestType.getLoginPwd())) {
                String salt = driverPasswordService.genPwdSalt();
                String encPwd = driverPasswordService.encryptPwd(requestType.getLoginPwd(),salt);
                drvPO.setLoginPwd(encPwd);
                drvPO.setSalt(salt);
            }
        }
        drvPO.setDrvFrom(TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode());
        drvPO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(requestType.getDrvProLineList()));
        if (CollectionUtils.isNotEmpty(requestType.getVaccinationTimeList())) {
            drvPO.setVaccinationTimeList(Joiner.on(",").join(requestType.getVaccinationTimeList()));
        }
        if (!Strings.isNullOrEmpty(requestType.getNucleicAcidTestingTime())) {
            java.util.Date date = DateUtil.stringToDate(requestType.getNucleicAcidTestingTime(), DateUtil.YYYYMMDD);
            if (date != null) {
                drvPO.setNucleicAcidTestingTime(new Date(date.getTime()));
            }
        }
        drvPO.setVersionFlag(requestType.getVersionFlag() == null?qconfig.getSystemVersionFlag():requestType.getVersionFlag());
        drvPO.setApproveSchedule(0);
        drvPO.setSupplierApproveSchedule(0);
        drvPO.setApproveTime(DateUtil.getNow());
        drvPO.setApproveAging(TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        Map<String,String> modSnapshotMap = Maps.newHashMap();
        modSnapshotMap.put("drvIdCard",drvPO.getDrvIdcard());
        modSnapshotMap.put("drvName",drvPO.getDrvName());
        drvPO.setModSnapshotValues(JsonUtil.toJson(modSnapshotMap));
        drvPO.setCertificateConfig(requestType.getCertificateConfigStr());
        if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),requestType.getInternalScope())){
            drvPO.setDrvHeadImg(StringUtils.isEmpty(requestType.getDrvHeadImg())?qconfig.getOverseasDefaultDrvHead():requestType.getDrvHeadImg());
            if(drvPO.getVehicleId() == null || drvPO.getVehicleId() <= 0){
                drvPO.setVehicleTypeId(null);
                drvPO.setVehicleLicense("");
            }
            drvPO.setPaiayEmail(TmsTransUtil.encrypt(requestType.getPaiayEmail(), KeyType.Mail));
        }
        drvPO.setOcrPassStatusJson(ocrPassJsonToString(requestType.getOcrPassStatusList(),SingleApproveTypeEnum.DRV));
        return drvPO;
    }

    private DrvRecruitingPO buildUpdateDrvPO(DrvVehRecruitingUpdateSOARequestType requestType){
        DrvRecruitingPO drvPO = new DrvRecruitingPO();
        BeanUtils.copyProperties(requestType,drvPO);
        drvPO.setOcrVaccineData(requestType.getOcrVaccineData());
        drvPO.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
        if(StringUtils.isNotEmpty(requestType.getExpiryBeginDate())){
            drvPO.setExpiryBeginDate(Date.valueOf(requestType.getExpiryBeginDate()));
        }
        if(StringUtils.isNotEmpty(requestType.getExpiryEndDate())){
            drvPO.setExpiryEndDate(Date.valueOf(requestType.getExpiryEndDate()));
        }
        drvPO.setDrvPhone(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone));
        drvPO.setEmail(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail));
        if(Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(),requestType.getInternalScope())){
            drvPO.setDrvIdcard(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card));
            if(StringUtils.isNotEmpty(requestType.getCertiDate())){
                drvPO.setCertiDate(Date.valueOf(requestType.getCertiDate()));
            }
        }
        drvPO.setDatachangeLasttime(DateUtil.getNow());
        drvPO.setVaccinationTimeList("");
        if (CollectionUtils.isNotEmpty(requestType.getVaccinationTimeList())) {
            drvPO.setVaccinationTimeList(Joiner.on(",").join(requestType.getVaccinationTimeList()));
        }
        drvPO.setNucleicAcidTestingTime(new Date(DateUtil.stringToDate("1995-01-01",DateUtil.YYYYMMDD).getTime()));
        if (!Strings.isNullOrEmpty(requestType.getNucleicAcidTestingTime())) {
            java.util.Date date = DateUtil.stringToDate(requestType.getNucleicAcidTestingTime(), DateUtil.YYYYMMDD);
            if (date != null) {
                drvPO.setNucleicAcidTestingTime(new Date(date.getTime()));
            }
        }
        drvPO.setNetAppealMaterials(requestType.getDrvNetAppealMaterials());
        if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),requestType.getInternalScope())){
            drvPO.setDrvHeadImg(StringUtils.isEmpty(requestType.getDrvHeadImg())?qconfig.getOverseasDefaultDrvHead():requestType.getDrvHeadImg());
            drvPO.setPaiayEmail(TmsTransUtil.encrypt(requestType.getPaiayEmail(), KeyType.Mail));
        }
        return drvPO;
    }

    private VehicleRecruitingPO buildUpdateVehPO(DrvVehRecruitingUpdateSOARequestType requestType){
        VehicleRecruitingPO vehPO = new VehicleRecruitingPO();
        BeanUtils.copyProperties(requestType,vehPO);
        vehPO.setDatachangeLasttime(DateUtil.getNow());
        if(StringUtils.isNotEmpty(requestType.getRegstDate())){
            vehPO.setRegstDate(Timestamp.valueOf(requestType.getRegstDate()+" 00:00:00"));
        }
        vehPO.setNetAppealMaterials(requestType.getVehNetAppealMaterials());
        vehPO.setNewOcrFieldValue(requestType.getNewOcrFieldValue());

        OcrComplianceDTO complianceRule = internationalEntryService.isInComplianceRuleGary(requestType.getCityId());
        if (Objects.nonNull(complianceRule)) {
            ComplianceVerificationResultDTO complianceResult = internationalEntryService.complianceVerification(complianceRule.getComplianceType(), requestType.getVehicleLicense(), requestType.getNewOcrFieldValue());
            vehPO.setAuditStatus(Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1);
            OcrPassStatusModelSOA ocrPassStatusModelSOA = new OcrPassStatusModelSOA();
            ocrPassStatusModelSOA.setOcrId(0L);
            ocrPassStatusModelSOA.setOcrItem(ApproveItemEnum.vehicle_compliance.getCode());
            ocrPassStatusModelSOA.setPassStatus(BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1)));
            requestType.getOcrPassStatusList().add(ocrPassStatusModelSOA);
        }
        return vehPO;
    }

    private Result<Boolean> resultErrorInfo(String code,String errorMsg,String infoBean){
        return Result.Builder.<Boolean>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg("【"+infoBean +"】"+errorMsg)
                .withData(false)
                .build();
    }

    private RecruitingApproveSOARequestType buildRequest(DrvVehRecruitingUpdateSOARequestType requestType){
        RecruitingApproveSOARequestType approveSOARequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO requestDTO = new RecruitingApproveSOARequestDTO();
        requestDTO.setAction(requestType.getAction());
        requestDTO.setMediumIdList(Arrays.asList(requestType.getDrvRecruitingId()));
        requestDTO.setRemark(requestType.getRemark());
        approveSOARequestType.setData(requestDTO);
        return approveSOARequestType;
    }

    public Result<Boolean> commonCheck(String drvPhone,String drvIdcard, String loginAccount,Integer internalScope,String email, Long supplierId, List<Integer> drvCategoryCode, List<Integer> vehicleCategoryCode){
        try {
            //校验产线是否包含包车
            if (productionLineUtil.isGraySupplierContainsDayProductLine(supplierId, drvCategoryCode) || productionLineUtil.isGraySupplierContainsDayProductLine(supplierId, vehicleCategoryCode) ){
                return this.resultErrorInfo(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getMessage()), String.valueOf(drvPhone));
            }

            //判断手机号唯一(境内、外)
            if (!Strings.isNullOrEmpty(drvPhone) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(drvPhone, KeyType.Phone), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists)).withData(false).build();
            }
            if (drvDrvierRepository.drvDispatchcheckDrvPhoneOnly(drvPhone) != null) {
                Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "addDrvVehRecruiting");
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists)).withData(false).build();
            }
            //判断邮箱唯一 (境内、外)
            if (!Strings.isNullOrEmpty(email) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(email, KeyType.Mail), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration)).withData(false).build();
            }
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), internalScope)) {
                //身份证唯一
                if (drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(drvIdcard, KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                            .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)).withData(false).build();
                }
            }else{
                if (Strings.isNullOrEmpty(loginAccount)) {
                    return Result.Builder.<Boolean>newResult().success().withData(true).build();
                }
                //账号唯一校验
                if (drvDrvierRepository.checkDrvOnly(loginAccount, TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists),loginAccount);
                }
                //待审批需校验司机审批中账号是否重复
                if (drvRecruitingRepository.checkDrvOnly(loginAccount, TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode(), ImmutableList.of(TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode(),TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode()))) {
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportMsgQunarAccountMustBeUniqueness),loginAccount);
                }
            }

            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     * 司机审批
     * */
    protected Result<Boolean> commonUpdateCheck(DrvVehRecruitingUpdateSOARequestType requestType,DrvRecruitingPO orginDrv) throws Exception {
        //判断手机号唯一
        if (!Strings.isNullOrEmpty(requestType.getDrvPhone()) && !Objects.equals(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone),orginDrv.getDrvPhone()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists),requestType.getDrvPhone());
        }
        //判断手机号带0/00唯一
        if (!Strings.isNullOrEmpty(requestType.getDrvPhone()) && !Objects.equals(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone), orginDrv.getDrvPhone()) && drvDrvierRepository.checkDrvPhoneOnly(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone), orginDrv.getDrvId())) {
            Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "updateDrvVehRecruiting");
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists), requestType.getDrvPhone());
        }
        //判断邮箱唯一
        if (!Strings.isNullOrEmpty(requestType.getEmail()) && !Objects.equals(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail),orginDrv.getEmail()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration),requestType.getEmail());
        }
        if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getInternalScope())) {
            //身份证唯一
            if (!Objects.equals(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card),orginDrv.getDrvIdcard()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists),requestType.getDrvIdcard());
            }
        } else {
            if (!Strings.isNullOrEmpty(requestType.getLoginAccount()) && !Objects.equals(requestType.getLoginAccount(),orginDrv.getLoginAccount())) {
                //账号唯一校验
                if (drvDrvierRepository.checkDrvOnly(requestType.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists),requestType.getLoginAccount());
                }
                //待审批需校验司机审批中账号是否重复
                if (drvRecruitingRepository.checkDrvOnly(requestType.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode(), ImmutableList.of(TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode(),TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode()))) {
                    return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists),requestType.getLoginAccount());
                }
            }

            //派安盈账户唯一校验
            if (!Strings.isNullOrEmpty(requestType.getPaiayAccount()) && !Objects.equals(requestType.getPaiayAccount(),orginDrv.getPaiayAccount()) && drvDrvierRepository.checkDrvOnly(requestType.getPaiayAccount(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST),requestType.getPaiayAccount());
            }
            //派安盈邮箱唯一校验
            if (!Strings.isNullOrEmpty(requestType.getPaiayEmail()) && !Objects.equals(TmsTransUtil.encrypt(requestType.getPaiayEmail(), KeyType.Mail),orginDrv.getPaiayEmail()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getPaiayEmail(), KeyType.Mail), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST),requestType.getPaiayEmail());
            }
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }


    /**
     * 司机招募审批
     * */
    public Result<Boolean> recruitingUpdateCheck(DrvVehRecruitingUpdateSOARequestType requestType,DrvRecruitingPO orginDrv) throws Exception {

        if(Objects.equals(requestType.getInternalScope(), AreaScopeTypeEnum.OVERSEAS.getCode()) || orginDrv.getVersionFlag().intValue() == 1){
            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }
        //判断手机号唯一
        if (!Strings.isNullOrEmpty(requestType.getDrvPhone()) && !Objects.equals(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone),orginDrv.getDrvPhone()) && drvRecruitingRepository.checkRecruitingDrvOnly(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists),requestType.getDrvPhone());
        }
        //身份证唯一
        if (!Objects.equals(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card),orginDrv.getDrvIdcard()) && drvRecruitingRepository.checkRecruitingDrvOnly(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists),requestType.getDrvIdcard());
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    /**
     * 车辆审批
     * */
    private Result<Boolean>  commonVehCheck(DrvVehRecruitingUpdateSOARequestType requestType,VehicleRecruitingPO orginVeh) throws Exception {
        try {
            //判断车牌号唯一
            if (!Objects.equals(requestType.getVehicleLicense(),orginVeh.getVehicleLicense()) && vehicleRepository.checkVehOnly(requestType.getVehicleLicense(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists),requestType.getVehicleLicense());
            }
            //判断Vin唯一
            if (!Objects.equals(requestType.getVin(),orginVeh.getVin()) && vehicleRepository.checkVehOnly(requestType.getVin(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists),requestType.getVin());
            }
            //二期之后的判断并且是境内做判断
            if(Objects.equals(enumRepository.getAreaScope(requestType.getCityId()), AreaScopeTypeEnum.OVERSEAS.getCode()) || orginVeh.getVersionFlag().intValue() == 1){
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            //判断车牌号唯一
            if (!Objects.equals(requestType.getVehicleLicense(),orginVeh.getVehicleLicense()) && vehicleRecruitingRepository.checkRecruitingVehOnly(requestType.getVehicleLicense(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists),requestType.getVehicleLicense());
            }
            //判断Vin唯一
            if (!Objects.equals(requestType.getVin(),orginVeh.getVin()) && vehicleRecruitingRepository.checkRecruitingVehOnly(requestType.getVin(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
                return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists),requestType.getVin());
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }

        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }


    /**
     * H5司机招募审批唯一性校验
     * */
    public Result<Boolean> recruitingUpdateCheckFromH5(DrvVehRecruitingUpdateFromH5RequestType requestType,DrvRecruitingPO orginDrv,VehicleRecruitingPO orginVeh) throws Exception {
        //正式身份证唯一
        if (!Objects.equals(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card),orginDrv.getDrvIdcard()) && drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists),requestType.getDrvIdcard());
        }
        //招募身份证唯一
        if (!Objects.equals(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card),orginDrv.getDrvIdcard()) && drvRecruitingRepository.checkRecruitingDrvOnly(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists),requestType.getDrvIdcard());
        }
        //正式车牌号唯一
        if (!Objects.equals(requestType.getVehicleLicense(),orginVeh.getVehicleLicense()) && vehicleRepository.checkVehOnly(requestType.getVehicleLicense(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists),requestType.getVehicleLicense());
        }
        //正式Vin唯一
        if (!Objects.equals(requestType.getVin(),orginVeh.getVin()) && vehicleRepository.checkVehOnly(requestType.getVin(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists),requestType.getVin());
        }
        //招募判断车牌号唯一
        if (!Objects.equals(requestType.getVehicleLicense(),orginVeh.getVehicleLicense()) && vehicleRecruitingRepository.checkRecruitingVehOnly(requestType.getVehicleLicense(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists),requestType.getVehicleLicense());
        }
        //招募Vin唯一
        if (!Objects.equals(requestType.getVin(),orginVeh.getVin()) && vehicleRecruitingRepository.checkRecruitingVehOnly(requestType.getVin(), TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
            return this.resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists),requestType.getVin());
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    public VehicleRecruitingPO buildH5UpdateVehPO(DrvVehRecruitingUpdateFromH5RequestType requestType){
        VehicleRecruitingPO vehPO = new VehicleRecruitingPO();
        BeanUtils.copyProperties(requestType,vehPO);
        vehPO.setDatachangeLasttime(DateUtil.getNow());
        if(StringUtils.isNotEmpty(requestType.getRegstDate())){
            vehPO.setRegstDate(Timestamp.valueOf(requestType.getRegstDate()+" 00:00:00"));
        }
        vehPO.setNetAppealMaterials(requestType.getVehNetAppealMaterials());
        return vehPO;
    }

    public DrvRecruitingPO buildH5UpdateDrvPO(DrvVehRecruitingUpdateFromH5RequestType requestType){
        DrvRecruitingPO drvPO = new DrvRecruitingPO();
        BeanUtils.copyProperties(requestType,drvPO);
        drvPO.setOcrVaccineData(requestType.getOcrVaccineData());
        drvPO.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
        if(StringUtils.isNotEmpty(requestType.getExpiryBeginDate())){
            drvPO.setExpiryBeginDate(Date.valueOf(requestType.getExpiryBeginDate()));
        }
        if(StringUtils.isNotEmpty(requestType.getExpiryEndDate())){
            drvPO.setExpiryEndDate(Date.valueOf(requestType.getExpiryEndDate()));
        }
        drvPO.setEmail(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail));
        drvPO.setDrvPhone(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone));
        drvPO.setDrvIdcard(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card));
        if(StringUtils.isNotEmpty(requestType.getCertiDate())){
            drvPO.setCertiDate(Date.valueOf(requestType.getCertiDate()));
        }
        drvPO.setVaccinationTimeList("");
        if (CollectionUtils.isNotEmpty(requestType.getVaccinationTimeList())) {
            drvPO.setVaccinationTimeList(Joiner.on(",").join(requestType.getVaccinationTimeList()));
        }
        drvPO.setNucleicAcidTestingTime(new Date(DateUtil.stringToDate("1995-01-01",DateUtil.YYYYMMDD).getTime()));
        if (!Strings.isNullOrEmpty(requestType.getNucleicAcidTestingTime())) {
            java.util.Date date = DateUtil.stringToDate(requestType.getNucleicAcidTestingTime(), DateUtil.YYYYMMDD);
            if (date != null) {
                drvPO.setNucleicAcidTestingTime(new Date(date.getTime()));
            }
        }
        drvPO.setNetAppealMaterials(requestType.getDrvNetAppealMaterials());
        return drvPO;
    }

    /**
    　* @description: 供应商编辑信息+单项审批
    　* <AUTHOR>
    　* @date 2021/12/14 19:31
    */
    public Result<Boolean> singleApproval(Integer versionFlag,DrvVehRecruitingUpdateSOARequestType requestType){
        if(versionFlag == null || versionFlag < 3 || CollectionUtils.isEmpty(requestType.getSingleList())){
            return Result.Builder.<Boolean>newResult().success().build();
        }
        try {
            SingleApprovalSOARequestType soaRequestType = new SingleApprovalSOARequestType();
            soaRequestType.setSingleList(requestType.getSingleList());
            soaRequestType.setModifyUser(requestType.getModifyUser());
            soaRequestType.setRecruitingId(requestType.getDrvRecruitingId());
            soaRequestType.setRecruitingType(requestType.getRecruitingType());
            recruitingCommandService.singleApproval(soaRequestType);
            return Result.Builder.<Boolean>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public RecruitingApproveSOARequestType buildH5ApproveRequest(DrvVehRecruitingUpdateFromH5RequestType requestType){
        RecruitingApproveSOARequestType approveSOARequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO requestDTO = new RecruitingApproveSOARequestDTO();
        requestDTO.setAction(0);
        requestDTO.setMediumIdList(Arrays.asList(requestType.getDrvRecruitingId()));
        requestDTO.setRemark(requestType.getRemark());
        requestDTO.setOperator(requestType.getModifyUser());
        requestDTO.setApproverAperation(TmsTransportConstant.ApproverAperationEnum.H5_mod_submit.getCode());
        approveSOARequestType.setData(requestDTO);
        return approveSOARequestType;
    }

    public Boolean syncNucleicVaccineChild(Map<String,Object> map,Long recruitingId,Integer versionFlag){
        if(MapUtils.isEmpty(map) || versionFlag == null || versionFlag < 3){
            return Boolean.FALSE;
        }
        List<TmsRecruitingApproveStepPO> approveStepPOList =  stepRepository.queryApproveStepList(recruitingId, TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(), CommonEnum.ApproveFromEnum.vbk.getValue());
        if(CollectionUtils.isEmpty(approveStepPOList)){
            return Boolean.FALSE;
        }
        List<Long> stepIdList = Lists.newArrayList();
        Map<Long,String> itemMap = Maps.newHashMap();
        for(TmsRecruitingApproveStepPO stepPO : approveStepPOList){
            if(Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.vaccine.getCode())||
                    Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode())){
                if(Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())||
                        Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode())){
                    stepIdList.add(stepPO.getId());
                    itemMap.put(stepPO.getId(),stepPO.getApproveItem() + "");
                }
            }
        }
        if(CollectionUtils.isEmpty(stepIdList)){
            return Boolean.FALSE;
        }
        List<TmsRecruitingApproveStepChildPO> childPOList =  childRepository.queryChildByStepIdList(stepIdList);
        if(CollectionUtils.isEmpty(childPOList)){
            return Boolean.FALSE;
        }
        for(TmsRecruitingApproveStepChildPO childPO : childPOList){
            String approveItem = itemMap.get(childPO.getRecruitingApproveStepId());
            if(StringUtils.isNotEmpty(approveItem) && map.get(NUCLEICACIDVACCINE_ITEM + approveItem)!=null){
                childPO.setCheckStatus((Integer) map.get(NUCLEICACIDVACCINE_ITEM + approveItem));
                if(map.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + approveItem)!=null){
                    childPO.setChildDesc(map.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC + approveItem) + "");
                childRepository.update(childPO);
                }
            }
        }
        return Boolean.TRUE;
    }

    public String getDrvModSnapshotValues(String modSnapshotValues,String drvIdCard,String drvName,List<SingleApprovalSOADTO> singleList){
        if(queryStepitem(singleList)){
            Map<String,String> modSnapshotMap = Maps.newHashMap();
            modSnapshotMap.put("drvIdCard",drvIdCard);
            modSnapshotMap.put("drvName",drvName);
            return JsonUtil.toJson(modSnapshotMap);
        }
        return modSnapshotValues;
    }

    public String getVehModSnapshotValues(String modSnapshotValues,String vehicleLicense,String vin,List<SingleApprovalSOADTO> singleList){
        if(queryStepitem(singleList)){
            Map<String,String> modSnapshotMap = Maps.newHashMap();
            modSnapshotMap.put("vehicleLicense",vehicleLicense);
            modSnapshotMap.put("vin",vin);
            return JsonUtil.toJson(modSnapshotMap);
        }
        return modSnapshotValues;
    }

    public Boolean queryStepitem(List<SingleApprovalSOADTO> singleList){
        if(CollectionUtils.isEmpty(singleList)){
            return Boolean.FALSE;
        }
        List<Long> stepIds = singleList.stream().map(SingleApprovalSOADTO::getApproveStepId).collect(Collectors.toList());
        List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepByIds(stepIds);
        if(CollectionUtils.isEmpty(stepPOList)){
            return Boolean.FALSE;
        }
        for(TmsRecruitingApproveStepPO stepPO : stepPOList){
            if(Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.identity.getCode())||
                    Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.Vehicle_icense.getCode())){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public Boolean judgeSingleStatus(List<SingleApprovalSOADTO> singleList){
        if(CollectionUtils.isEmpty(singleList)){
            return Boolean.FALSE;
        }
        List<Long> approveStepId = singleList.stream().map(SingleApprovalSOADTO::getApproveStepId).collect(Collectors.toList());
        List<TmsRecruitingApproveStepPO> stepPOList  =  stepRepository.queryApproveStepByIds(approveStepId);
        List<Long> ids = Lists.newArrayList();
        for(TmsRecruitingApproveStepPO stepPO : stepPOList){
            if(!Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())){
                ids.add(stepPO.getId());
            }
        }
        //操作单项中如果不是待审核，则单项审批失败
        if(ids.size() > 0){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean checkNowRoleApproveStatus(Integer approveStatus,Long cityId,Integer versionFlag){
        Map<String, String> map = SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(map) || map.get("accountType")==null){
            return Boolean.FALSE;
        }
        if(Objects.equals(enumRepository.getAreaScope(cityId),AreaScopeTypeEnum.OVERSEAS.getCode().intValue())){
            return Boolean.FALSE;
        }
        if(versionFlag < 3 ){
            return Boolean.FALSE;
        }
        Integer approveFrom = Integer.parseInt(map.get("accountType"));
        if(Objects.equals(approveFrom, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
            if(!Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode())&&
                    !Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
    　* @description: 如果身份证变更：审批驾驶证时，判断身份证单项状态为通过和不通过时返602
                                    审批身份证时，判断驾驶单项状态为通过和不通过时返602
    　* <AUTHOR>
    　* @date 2022/2/19 14:08
    */
    public Boolean checkDrvIdCardStepStatus(Long recruitingId,String newDrvIdcard,String orgDrvIdcard,List<SingleApprovalSOADTO> singleList){
        if(Objects.equals(TmsTransUtil.encrypt(newDrvIdcard, KeyType.Identity_Card),orgDrvIdcard)){
            return Boolean.FALSE;
        }
        if(CollectionUtils.isEmpty(singleList)){
            return Boolean.FALSE;
        }
        List<Long> approveStepId = singleList.stream().map(SingleApprovalSOADTO::getApproveStepId).collect(Collectors.toList());
        if(approveStepId.size() >= 2){
            return Boolean.FALSE;
        }
        List<TmsRecruitingApproveStepPO> stepPOList  =  stepRepository.queryApproveStepByIds(approveStepId);
        Boolean driverlicenseItemFlag = Boolean.FALSE;
        Boolean idcardFlag = Boolean.FALSE;
        for(TmsRecruitingApproveStepPO stepPO : stepPOList){
            if(Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.driverlicense.getCode())){
                driverlicenseItemFlag = Boolean.TRUE;
                break;
            }
            if(Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.identity.getCode())){
                idcardFlag = Boolean.TRUE;
                break;
            }
        }
        if(!idcardFlag && !driverlicenseItemFlag){
            return Boolean.FALSE;
        }
        //判断身份证单项是否待审批
        Integer approveItem = TmsTransportConstant.ApproveItemEnum.driverlicense.getCode();
        if(driverlicenseItemFlag){
            approveItem = TmsTransportConstant.ApproveItemEnum.identity.getCode();
        }
        List<TmsRecruitingApproveStepPO> stepPOList1 =  stepRepository.queryApproveStepList(recruitingId, TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(),approveItem, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue());
        if(CollectionUtils.isEmpty(stepPOList1)){
            return Boolean.FALSE;
        }
        for(TmsRecruitingApproveStepPO stepPO : stepPOList1){
            if(Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode())||
                    Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode())){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public StepApproveNoPassVO buildWorkStepApproveNoPassVO(DrvVehRecruitingUpdateSOARequestType requestType,Integer drvFrom,Long vehicleId){
        StepApproveNoPassVO passVO = new StepApproveNoPassVO();
        BeanUtils.copyProperties(requestType,passVO);
        passVO.setDrvFrom(drvFrom);
        passVO.setVehicleId(vehicleId);
        return passVO;
    }

    public StepApproveNoPassVO buildH5StepApproveNoPassVO(DrvVehRecruitingUpdateFromH5RequestType requestType,Integer drvFrom,Long vehicleId){
        StepApproveNoPassVO passVO = new StepApproveNoPassVO();
        BeanUtils.copyProperties(requestType,passVO);
        passVO.setDrvFrom(drvFrom);
        passVO.setVehicleId(vehicleId);
        return passVO;
    }

    public List<Integer> sigleApproveNoPass(StepApproveNoPassVO passVO){
        List<Integer> stepItems = Lists.newArrayList();
        if(passVO == null){
            return stepItems;
        }
        switch (passVO.getRecruitingType()){
            //1:工作台/h5创建司机
            case 1:
                if(netDrvProperty(passVO)){
                    stepItems.add(TmsTransportConstant.ApproveItemEnum.net_people.getCode());
                }
                if(nucleicAcidProperty(passVO)){
                    stepItems.add(TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode());
                }
                if(vaccineProperty(passVO)){
                    stepItems.add(TmsTransportConstant.ApproveItemEnum.vaccine.getCode());
                }
                operationStepApproveStatus(stepItems,passVO.getDrvRecruitingId(), TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),passVO.getModifyUser());
                if(Objects.equals(passVO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode()) && netVehicleProperty(passVO)){
                    operationStepApproveStatus(Arrays.asList(TmsTransportConstant.ApproveItemEnum.net_vehile.getCode()),passVO.getVehicleId(), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),passVO.getModifyUser());
                }
                break;
                //2:工作台创建的车辆
            case 2:
                if(netVehicleProperty(passVO)){
                    operationStepApproveStatus(Arrays.asList(TmsTransportConstant.ApproveItemEnum.net_vehile.getCode()),passVO.getDrvRecruitingId(), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),passVO.getModifyUser());
                }
        }

        return stepItems;
    }

    //处理单项不审批
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean operationStepApproveStatus(List<Integer> stepItems,Long drvRecruitingId,Integer recruitingType,String modifyUser){
        if(CollectionUtils.isEmpty(stepItems)){
            return Boolean.FALSE;
        }
        try {
            List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepByItems(drvRecruitingId, recruitingType, stepItems);
            if(CollectionUtils.isEmpty(stepPOList)){
                return Boolean.FALSE;
            }

            //去掉不审批的单项
            for(Iterator<TmsRecruitingApproveStepPO> iterator = stepPOList.iterator();iterator.hasNext();){
                TmsRecruitingApproveStepPO approveStepPO = iterator.next();
                if(Objects.isNull(approveStepPO)){
                    continue;
                }
                if(Objects.equals(approveStepPO.getApproveItem(), TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                    iterator.remove();
                }
            }
            if(CollectionUtils.isEmpty(stepPOList)){
                return Boolean.FALSE;
            }

            List<Long> stepIds = stepPOList.stream().map(TmsRecruitingApproveStepPO::getId).collect(Collectors.toList());
            //单项置为不审批
            stepRepository.updateApproveStatusByIds(stepIds, TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode(),modifyUser);
            //处理三方
            if(stepItems.contains(TmsTransportConstant.ApproveItemEnum.net_people.getCode())){
                checkRepository.updateCertificateActive(drvRecruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                        TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(),Boolean.FALSE,modifyUser);

            }
            if(stepItems.contains(TmsTransportConstant.ApproveItemEnum.net_vehile.getCode())){
                checkRepository.updateCertificateActive(drvRecruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode(),
                        TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode(),Boolean.FALSE,modifyUser);

            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //网约车车证单项属性判断
    public Boolean netVehicleProperty(StepApproveNoPassVO passVO){
        if(StringUtils.isEmpty(passVO.getNetTansCtfctImg()) && StringUtils.isEmpty(passVO.getVehNetAppealMaterials())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //网约车人证单项属性判断
    public Boolean netDrvProperty(StepApproveNoPassVO passVO){
        if(StringUtils.isEmpty(passVO.getOtherCertificateImg()) && StringUtils.isEmpty(passVO.getDrvNetAppealMaterials())&&
                StringUtils.isEmpty(passVO.getNetVehiclePeoImg())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //核酸报告单项属性判断
    public Boolean nucleicAcidProperty(StepApproveNoPassVO passVO){
        if(StringUtils.isEmpty(passVO.getNucleicAcidReportImg()) && StringUtils.isEmpty(passVO.getNucleicAcidTestingTime())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //疫苗报告单项属性判断
    public Boolean vaccineProperty(StepApproveNoPassVO passVO){
        if(StringUtils.isEmpty(passVO.getVaccineReportImg()) && CollectionUtils.isEmpty(passVO.getVaccinationTimeList())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //境外H5入注，如果OCR识别通过，则直接审批通过进入正式表，如果OCR识别不通过，则进入供应商审批
    public Result<Boolean> overseasIsBusiness(DrvVehRecruitingAddSOARequestType requestType,Long drvRecruitingId,Long vehRecruitingId){
        try {
            //判断当前供应商是否在灰度逻辑中并且是境外数据
            if(driverQueryService.overseasIsBusiness(requestType.getSupplierId(),requestType.getVersionFlag(),requestType.getInternalScope(), BusinessTypeEnum.H5)){
                // 如果OCR识别通过，则直接审批通过 且 合规字段字段判断合规
                if(CtripCommonUtils.ocrIsPass(requestType.getOcrPassStatusList())){
                    //直接审批通过置正式司机车辆
                    Result<Boolean>  result = recruitingCommandService.recruitingUpdatePass(Arrays.asList(drvRecruitingId),requestType.getDrvName(),Boolean.TRUE,"",TmsTransportConstant.RecruitingTypeEnum.drv.getCode(), new ArrayList<>());
                    if(!result.isSuccess()){
                        return Result.Builder.<Boolean>newResult().fail().withCode(result.getCode()).withMsg(result.getMsg()).build();
                    }
                    //生成单项数据，单项都为通过
                    recruitingCommandService.initOverseasSingleData(drvRecruitingId,vehRecruitingId,SingleApproveTypeEnum.DRV,requestType.getOcrPassStatusList(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public String ocrPassJsonToString(List<OcrPassStatusModelSOA> ocrPassStatusList,SingleApproveTypeEnum typeEnum){
        if(CollectionUtils.isEmpty(ocrPassStatusList)){
            return "";
        }
        List<OcrPassStatusModelSOA> soaList = Lists.newArrayList();
        for(OcrPassStatusModelSOA modelSOA : ocrPassStatusList){
            if(Objects.equals(typeEnum.getCode(),SingleApproveTypeEnum.VEH.getCode()) &&
                    (Objects.equals(modelSOA.getOcrItem(), ApproveItemEnum.vehicle_number.getCode()) || Objects.equals(modelSOA.getOcrItem(), ApproveItemEnum.vehicle_compliance.getCode()) )){
                OcrPassStatusModelSOA statusModelSOA  = new OcrPassStatusModelSOA();
                BeanUtils.copyProperties(modelSOA,statusModelSOA);
                soaList.add(statusModelSOA);
            }
            if(Objects.equals(typeEnum.getCode(),SingleApproveTypeEnum.DRV.getCode()) && Objects.equals(modelSOA.getOcrItem(), ApproveItemEnum.drv_name.getCode())){
                OcrPassStatusModelSOA statusModelSOA  = new OcrPassStatusModelSOA();
                BeanUtils.copyProperties(modelSOA,statusModelSOA);
                soaList.add(statusModelSOA);
            }
        }
        if(CollectionUtils.isEmpty(soaList)){
            return "";
        }
        return JsonUtil.toJson(soaList);
    }

    //编辑招募信息，如果当前供应商在灰度中，并且是境外新逻辑，则判断OCR是否通过
    public Result<Boolean> overseasRecruitingAuto(DrvVehRecruitingUpdateSOARequestType requestType,Integer versionFlag,Integer areaScope, Long drvRecruitingId,Long vehRecruitingId,RecruitingTypeEnum recruitingTypeEnum,SingleApproveTypeEnum approveTypeEnum){
        try {
            if(driverQueryService.overseasIsBusiness(requestType.getSupplierId(),versionFlag,areaScope,BusinessTypeEnum.OTHER) && requestType.getApproveMark()!=null && Objects.equals(requestType.getApproveMark(),OverseasApproveMarkEnum.approve.getCode())){
                //如果OCR校验通过,则直接进入正式表,如果OCR没有传或不通过，则进入平台审批 还要判断是车还是人
                if(CtripCommonUtils.ocrIsPass(requestType.getOcrPassStatusList())){
                    //直接审批通过置正式司机车辆
                    Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Arrays.asList(drvRecruitingId),requestType.getModifyUser(),Boolean.TRUE,"", recruitingTypeEnum.getCode(), new ArrayList<>());
                    if(!result.isSuccess()){
                        return Result.Builder.<Boolean>newResult().fail().withCode(result.getCode()).withMsg(result.getMsg()).build();
                    }
                }else {
                    //进入平台审批流程
                    Result<Boolean> result = recruitingCommandService.recruitingUpdateStatus(Arrays.asList(drvRecruitingId), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(), SessionHolder.getRestSessionAccountName(), "", recruitingTypeEnum.getCode(), TmsTransportConstant.ApproverAperationEnum.work_register_submit.getCode(), new ArrayList<>());
                    if(!result.isSuccess()){
                        return Result.Builder.<Boolean>newResult().fail().withCode(result.getCode()).withMsg(result.getMsg()).build();
                    }
                }
                //生成单项数据
                recruitingCommandService.initOverseasSingleData(drvRecruitingId,vehRecruitingId,approveTypeEnum,requestType.getOcrPassStatusList(), requestType.getModifyUser());
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

}
