package com.ctrip.dcs.tms.transport.application.query.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.basebiz.ai.aiplatform.contract.api.AiPlatformServiceClient;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformRequest;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformResponse;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ComplianceStrategyEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.InternationalEntryConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.NewOcrFiledConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalSnowFlakeUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

@Service
public class InternationalEntryServiceImpl implements InternationalEntryService {
    private Logger logger = LoggerFactory.getLogger(InternationalEntryServiceImpl.class);
    @Autowired
    private InternationalEntryConfig internationalEntryConfig;

    @Autowired
    private NewOcrFiledConfig newOcrFiledConfig;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    @Qualifier("aiPlatformServiceClient")
    private AiPlatformServiceClient aiPlatformServiceClient;

    private LocalSnowFlakeUtil localSnowFlakeUtil = new LocalSnowFlakeUtil();


    /**
     * 是否在安全合规的范围内
     *
     * @param cityId 城市ID
     * @param supplierId 供应商id
     * @return {@link Result }<{@link Boolean }>
     */
    @Override
    public Result<Boolean> isInComplianceRuleGary(Long cityId, Long supplierId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.isInComplianceRuleGary");
        String methodName = "isInComplianceRuleGary";
        Result<Boolean> result = null;
        
        try {
            // 记录入参日志
            logger.info("isInComplianceRuleGary","{} 方法开始执行，入参: cityId={}, supplierId={}", methodName, cityId, supplierId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            List<Long> complianceGaryCountry = internationalEntryConfig.getComplianceGaryCountry();
            List<Long> complianceGaryCity = internationalEntryConfig.getComplianceGaryCity();

            if (complianceGaryCity.contains(cityId)) {
                result = Result.Builder.<Boolean>newResult().success().withData(true).build();
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            Long countryId = enumRepository.getCountryId(cityId);
            if (complianceGaryCountry.contains(countryId)) {
                result = Result.Builder.<Boolean>newResult().success().withData(true).build();
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            result = Result.Builder.<Boolean>newResult().success().withData(false).build();
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("isInComplianceRuleGary","{} 方法执行异常，入参: cityId={}, supplierId={}", methodName, cityId, supplierId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("isInComplianceRuleGary","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    /**
     * 查询所需字段列表
     *
     * @param cityId     城市ID
     * @param supplierId 供应商id
     * @param sceneList
     * @return {@link Result }<{@link List }<{@link String }>>
     */
    @Override
    public Result<List<RequiredFieldDTO>> queryRequiredFiledList(Long cityId, Long supplierId, List<String> sceneList) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.queryRequiredFiledList");
        String methodName = "queryRequiredFiledList";
        Result<List<RequiredFieldDTO>> result = null;
        
        try {
            // 记录入参日志
            logger.info("queryRequiredFiledList","{} 方法开始执行，入参: cityId={}, supplierId={}, sceneList={}", methodName, cityId, supplierId, JsonUtil.toJson(sceneList));
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            List<RequiredFieldDTO> resultList = newOcrFiledConfig.getFieldListByCityWithFallback(cityId, sceneList);

            if (CollectionUtils.isNotEmpty(resultList)) {
                result = Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(resultList).build();
            } else {
                result = Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(null).build();
            }
            
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("queryRequiredFiledList","{} 方法执行异常，入参: cityId={}, supplierId={}, sceneList={}", methodName, cityId, supplierId, JsonUtil.toJson(sceneList), e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("queryRequiredFiledList","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    @Override
    public Result<List<OcrResultDTO>> ocrRecognition(OcrReqDTO ocrReqDTO) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.ocrRecognition");
        String methodName = "ocrRecognition";
        Result<List<OcrResultDTO>> result = null;
        
        try {
            // 记录入参日志
            logger.info("ocrRecognition","{} 方法开始执行，入参: {}", methodName, JsonUtil.toJson(ocrReqDTO));
            Cat.logEvent("InternationalEntry.Method.Start", methodName);

            List<OcrResultDTO> resultDTOS = new ArrayList<>();
            Long cityId = ocrReqDTO.getCityId();
            String imgType = ocrReqDTO.getImgType();
            String imgUrl = ocrReqDTO.getImgUrl();
            String newDomain = internationalEntryConfig.getImageUrlPrefix();
            String newUrl = imgUrl.replaceFirst(internationalEntryConfig.getImageReplaceRegular(), newDomain).replace("https://","http://");

            OcrRecognitionResultDTO recognitionResultDTO = newOcrFiledConfig.getOcrRecognitionResultByCityWithFallback(cityId, imgType);
            if (Objects.isNull(recognitionResultDTO)) {
                result = Result.Builder.<List<OcrResultDTO>>newResult().success().withData(null).build();
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            long imageId = localSnowFlakeUtil.nextId();
            Map<String, String> extData = new HashMap<>(16);
            extData.put("imageId", String.valueOf(imageId));
            extData.put("image", newUrl);
            extData.put("inputType", "url");
            extData.put("mode", "extract");
            extData.put("imageLabel", recognitionResultDTO.getReqType());

            AiPlatformRequest aiPlatformRequest = new AiPlatformRequest();
            aiPlatformRequest.setBiztype("voucherImageRec");
            aiPlatformRequest.setAppId("100025330");
            aiPlatformRequest.setService("imageService");
            aiPlatformRequest.setRequestData(JsonUtil.toJson(extData));

            List<OcrRespDTO> response = recognitionResultDTO.getResponse();

            try {
                // RPC请求日志
                logger.info("ocrRecognition","AI平台OCR识别RPC请求开始，请求参数: {}", JsonUtil.toJson(aiPlatformRequest));
                Cat.logEvent("RPC.Request", "AiPlatformService.execute");
                
                AiPlatformResponse resp = aiPlatformServiceClient.execute(aiPlatformRequest);
                
                // RPC响应日志
                logger.info("ocrRecognition","AI平台OCR识别RPC请求完成，响应结果: {}", JsonUtil.toJson(resp));
                Cat.logEvent("RPC.Response", "AiPlatformService.execute");
                
                Integer returnCode = resp.getReturnCode();
                if (Objects.equals(returnCode, 200)) {
                    String responseData = resp.getResponseData();
                    OcrRecognitionDTO ocrRecognitionDTO = JsonUtil.fromJson(responseData, new TypeReference<OcrRecognitionDTO>() {});
                    String returnCode1 = ocrRecognitionDTO.getReturn_code();
                    if (StringUtils.equals(returnCode1, "200")) {
                        List<ExtractResult> resExtract = ocrRecognitionDTO.getRes_extract();
                        Map<String, String> collect = Optional.ofNullable(resExtract).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(ExtractResult::getKey, ExtractResult::getValue));
                        if (CollectionUtils.isNotEmpty(response)) {
                            response.forEach(ocrRespDTO -> {
                                String field = ocrRespDTO.getField();
                                String value = collect.get(field);
                                OcrResultDTO ocrResultDTO = new OcrResultDTO();
                                ocrResultDTO.setFieldName(field);
                                ocrResultDTO.setBackFillField(ocrRespDTO.getBackFillField());
                                ocrResultDTO.setValue(value);
                                ocrResultDTO.setBackfill(ocrRespDTO.getRequired());
                                ocrResultDTO.setOcrId(imageId);
                                resultDTOS.add(ocrResultDTO);
                            });
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("ocrRecognition","AI平台OCR识别RPC请求异常，请求参数: {}", JsonUtil.toJson(aiPlatformRequest), e);
                Cat.logError("RPC请求异常", e);
            }
            
            result = Result.Builder.<List<OcrResultDTO>>newResult().success().withData(resultDTOS).build();
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("ocrRecognition","{} 方法执行异常，入参: {}", methodName, JsonUtil.toJson(ocrReqDTO), e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("ocrRecognition","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    /**
     * 合规性验证 返回合规状态和具体原因
     * 场景  ：新增招募、更新招募信息、新增车辆、更新车辆
     * @param inComplianceRule 合规规则
     * @param vehicleLicense   车辆牌照
     * @param newOcrFieldValue 新ocr字段值
     * @return ComplianceVerificationResultDTO 包含合规状态和不合规原因
     */
    @Override
    public ComplianceVerificationResultDTO complianceVerification(Integer inComplianceRule, String vehicleLicense, String newOcrFieldValue) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.complianceVerification");
        String methodName = "complianceVerification";
        ComplianceVerificationResultDTO result = null;

        try {
            // 记录入参日志
            logger.info("complianceVerification","{} 方法开始执行，入参: inComplianceRule={}, vehicleLicense={}, newOcrFieldValue={}",
                       methodName, inComplianceRule, vehicleLicense, newOcrFieldValue);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);

            if (ComplianceStrategyEnum.FIRST_STRATEGY.getCode().equals(inComplianceRule)) {
                result = japanCompliance(vehicleLicense, newOcrFieldValue);
            }else if (ComplianceStrategyEnum.SECOND_STRATEGY.getCode().equals(inComplianceRule)) {
                result = singaporeCompliance(vehicleLicense, newOcrFieldValue);
            }else if (ComplianceStrategyEnum.THIRD_STRATEGY.getCode().equals(inComplianceRule)) {
                result = koreaCompliance(vehicleLicense);
            } else {
                result = ComplianceVerificationResultDTO.manualReview("未知的合规规则: " + inComplianceRule);
            }

            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("complianceVerification","{} 方法执行异常，入参: inComplianceRule={}, vehicleLicense={}, newOcrFieldValue={}",
                       methodName, inComplianceRule, vehicleLicense, newOcrFieldValue, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("complianceVerification","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    private ComplianceVerificationResultDTO koreaCompliance(String vehicleLicense) {
        String methodName = "koreaCompliance";
        logger.info("koreaCompliance","{} 方法开始执行，入参: vehicleLicense={}", methodName, vehicleLicense);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceVerificationResultDTO result = null;
        try {
            AtomicReference<Boolean> koreaVehivleApprove = new AtomicReference<>(false);
            if (StringUtils.isNotBlank(vehicleLicense)) {
                List<String> approveVehicleList = internationalEntryConfig.getKoreaApproveVehicleList();
                vehicleLicense.chars().forEach(c -> {
                    if (approveVehicleList.contains(c + "")) {
                        koreaVehivleApprove.set(true);
                    }
                });
            }

            if (BooleanUtils.isTrue(koreaVehivleApprove.get())) {
                result = ComplianceVerificationResultDTO.compliant();
            } else {
                result = ComplianceVerificationResultDTO.manualReview("韩国车牌号码不包含批准的字符");
            }

            return result;
        } finally {
            if (result != null) {
                logger.info("koreaCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }

    private ComplianceVerificationResultDTO singaporeCompliance(String vehicleLicense, String newOcrFieldValue) {
        String methodName = "singaporeCompliance";
        logger.info("singaporeCompliance","{} 方法开始执行，入参: vehicleLicense={}, newOcrFieldValue={}", methodName, vehicleLicense, newOcrFieldValue);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceVerificationResultDTO result = null;
        try {
            Map<String,  Map<String,String>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, Map<String,String>>>() {});
            Map<String, String> stringStringMap2 = stringStringMap.get("complianceQualificationCertificates");
            if (MapUtils.isEmpty(stringStringMap2)) {
                result = ComplianceVerificationResultDTO.nonCompliant("合规资质证书信息为空");
                return result;
            }
            String vehicleNature = stringStringMap2.get("VehicleNature");
            String licensePlateNo = stringStringMap2.get("LicensePlateNo");
            String vehicleScheme = stringStringMap2.get("VehicleScheme");

            boolean startsWithP = vehicleLicense.startsWith("P");
            boolean startsWithSH = vehicleLicense.startsWith("SH");
            boolean privateHire = vehicleNature != null && vehicleNature.toLowerCase().contains("private hire");
            boolean publicServiceVehicle = vehicleScheme != null && vehicleScheme.toLowerCase().contains("public service vehicle");
            boolean equals = StringUtils.equals(licensePlateNo, vehicleLicense);

            if (startsWithSH) {
                result = ComplianceVerificationResultDTO.compliant();
                return result;
            }
            if (startsWithP) {
                if (publicServiceVehicle && equals) {
                    result = ComplianceVerificationResultDTO.compliant();
                    return result;
                }
                if (!equals && !publicServiceVehicle) {
                    result = ComplianceVerificationResultDTO.nonCompliant("P开头车牌号码不匹配且不是公共服务车辆");
                    return result;
                }
                result = ComplianceVerificationResultDTO.manualReview("P开头车牌需要人工审核");
                return result;
            }
            if (privateHire) {
                result = ComplianceVerificationResultDTO.compliant();
                return result;
            }
            result = ComplianceVerificationResultDTO.manualReview("车辆性质不是私人租赁，需要人工审核");
            return result;
        } finally {
            if (result != null) {
                logger.info("singaporeCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }



    @Nullable
    private ComplianceVerificationResultDTO japanCompliance(String vehicleLicense, String newOcrFieldValue) {
        String methodName = "japanCompliance";
        logger.info("japanCompliance","{} 方法开始执行，入参: vehicleLicense={}, newOcrFieldValue={}", methodName, vehicleLicense, newOcrFieldValue);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceVerificationResultDTO result = null;
        try {
            if (StringUtils.isNotBlank(newOcrFieldValue)) {
                Map<String,  Map<String,String>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, Map<String,String>>>() {});
                Map<String, String> stringStringMap1 = stringStringMap.get("vehicleCertiImg");
                if (stringStringMap1 == null) {
                    result = ComplianceVerificationResultDTO.manualReview("车辆证书图片信息为空");
                    return result;
                }
                String useType = stringStringMap1.get("useType");
                Boolean useAge = false;
                AtomicReference<Boolean> vehivleApprove = new AtomicReference<>(false);

                if (StringUtils.isNotBlank(useType) && useType.equals("事业用")) {
                    useAge = true;
                }
                if (StringUtils.isNotBlank(vehicleLicense)) {
                    List<String> approveVehicleList = internationalEntryConfig.getJapanApproveVehicleList();
                    vehicleLicense.chars().forEach(c -> {
                        if (approveVehicleList.contains(c + "")) {
                            vehivleApprove.set(true);
                        }
                    });
                }
                if (BooleanUtils.isTrue(useAge) && BooleanUtils.isTrue(vehivleApprove.get())) {
                    result = ComplianceVerificationResultDTO.compliant();
                    return result;
                }
                if (BooleanUtils.isFalse(useAge) && BooleanUtils.isFalse(vehivleApprove.get())) {
                    result = ComplianceVerificationResultDTO.nonCompliant("车辆用途不是事业用且车牌号码不包含批准的字符");
                    return result;
                }
                result = ComplianceVerificationResultDTO.manualReview("车辆用途或车牌号码部分符合要求，需要人工审核");
                return result;
            }
            result = ComplianceVerificationResultDTO.manualReview("OCR字段值为空，需要人工审核");
            return result;
        } finally {
            if (result != null) {
                logger.info("japanCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }

    @Override
    public OcrComplianceDTO isInComplianceRuleGary(Long cityId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.isInComplianceRuleGary_overload");
        String methodName = "isInComplianceRuleGary(cityId)";
        OcrComplianceDTO result = null;
        
        try {
            // 记录入参日志
            logger.info("isInComplianceRuleGary","{} 方法开始执行，入参: cityId={}", methodName, cityId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            result = newOcrFiledConfig.getOcrComplianceByCityWithFallback(cityId);
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("isInComplianceRuleGary","{} 方法执行异常，入参: cityId={}", methodName, cityId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            logger.info("isInComplianceRuleGary","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
            Cat.logEvent("InternationalEntry.Method.End", methodName);
            transaction.complete();
        }
    }

    @Override
    public InternatSettleOCRDTO useNewOCR(Long cityId, Long supplierId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.useNewOCR");
        String methodName = "useNewOCR";
        InternatSettleOCRDTO result = null;
        
        try {
            // 记录入参日志
            logger.info("useNewOCR","{} 方法开始执行，入参: cityId={}, supplierId={}", methodName, cityId, supplierId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            InternatSettleOCRDTO internatSettleOCRDTO = new InternatSettleOCRDTO();
            NewOCRDTO newOcr = newOcrFiledConfig.getNewOcrByCityWithFallback(cityId);
            if (Objects.nonNull(newOcr)) {
                internatSettleOCRDTO.setUseNewOCR(true);
                internatSettleOCRDTO.setNewOCRFieldList(newOcr.getOcrFieldList());
                result = internatSettleOCRDTO;
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            internatSettleOCRDTO.setUseNewOCR(false);
            internatSettleOCRDTO.setNewOCRFieldList(new ArrayList<>());
            result = internatSettleOCRDTO;
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("useNewOCR","{} 方法执行异常，入参: cityId={}, supplierId={}", methodName, cityId, supplierId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("useNewOCR","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

}
