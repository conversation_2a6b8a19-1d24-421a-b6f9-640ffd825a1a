package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.scm.sdk.domain.SupplierRepository;
import com.ctrip.dcs.tms.transport.api.model.CategorySOADTO;
import com.ctrip.dcs.tms.transport.api.model.DrvAddSOARequestType;
import com.ctrip.dcs.tms.transport.application.command.impl.DriverAccountManagementHelper;
import com.ctrip.dcs.tms.transport.application.command.impl.DriverCommandServiceImpl;
import com.ctrip.dcs.tms.transport.application.command.impl.TmsQmqProducerCommandServiceImpl;
import com.ctrip.dcs.tms.transport.application.command.impl.TransportGroupCommandServiceImpl;
import com.ctrip.dcs.tms.transport.application.helper.DrvStatusTransitionPermissionHelper;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService;
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupBasePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvInfoCacheDto;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.DrvDrvierRepositoryImpl;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryImpl;
import com.ctrip.igt.framework.common.result.Result;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;

@RunWith(MockitoJUnitRunner.class)
public class DriverCommandServiceTest {

    @InjectMocks
    private DriverCommandServiceImpl driverCommandService;
    @Mock
    private DrvDrvierRepositoryImpl repository;
    @Mock
    private TransportGroupCommandServiceImpl transportGroupCommandService;
    @Mock
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Mock
    private TmsQmqProducerCommandServiceImpl tmsQmqProducerCommandService;
    @Mock
    private ApprovalProcessAuthQconfig authQconfig;
    @Mock
    EnumRepositoryImpl enumRepository;
    @Mock
    SupplierRepository supplierRepository;
    @Mock
    private TmsPmsproductQueryService queryService;
    @Mock
    private DrvRecruitingRepository recruitingRepository;
    @Mock
    DriverGroupRelationRepository driverGroupRelationRepository;
    @Mock
    DriverQueryService driverQueryService;
    @Mock
    TmsTransportQconfig tmsTransportQconfig;
    @Mock
    DrvDrvierRepository drvDrvierRepository;
    @Mock
    TmsDrvFreezeRepository tmsDrvFreezeRepository;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    TmsTransportApproveCommandService approveCommandService;
    @Mock
    DriverAccountManagementHelper driverAccountManagementHelper;

    @Mock
    TransportCommonQconfig commonQconfig;

    @Mock
    MobileHelper mobileUtil;

    @Mock
    ProductionLineUtil productionLineUtil;

    @Mock
    QueryCategoryService queryCategoryService;

    @Mock
    BusinessQConfig businessQConfig;

    @Mock
    DrvStatusTransitionPermissionHelper drvStatusTransitionPermissionHelper;

    @Before
    public void init() throws SQLException {
        Mockito.when(mobileUtil.isMobileValid(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(drvStatusTransitionPermissionHelper.checkPermission(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
    }


    @Test
    public void updateDrvStatus() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Arrays.asList(1L))).thenReturn(drvDriverPOList);
        Result result = Result.Builder.<Boolean>newResult().success().withData(true).build();
//        Mockito.when(driverQueryService.judgeDrvOnlinePermission(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(3),Arrays.asList(1L),Arrays.asList(1L))).thenReturn(result);
//        Mockito.when(drvDrvierRepository.updateDrvStatus(Arrays.asList(1L),1, 3,"system")).thenReturn(1);
//        Mockito.when(drvDrvierRepository.unbindCarforDrv(Arrays.asList(1L),"system")).thenReturn(1);
//        Mockito.doAnswer(invocation -> null).when(transportGroupCommandService).unBoundTransport(Arrays.asList(1L),"system",true);
//        Mockito.doAnswer(invocation -> null).when(tmsQmqProducerCommandService).sendDrvOfflineQmq(Arrays.asList(1L));
//        Mockito.when(tmsDrvFreezeRepository.batchUnFreezeByDrvIds(Arrays.asList(1L),"system")).thenReturn(1);
        Result<Boolean> result1 = driverCommandService.updateDrvStatus(Arrays.asList(1L),3,1,"system");
        Assert.assertTrue(result1.isSuccess());
    }

    @Test
    public void addDrv() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setDrvName("111");
        requestType.setDrvEnglishName("111");
        requestType.setDrvLanguage("EN");
        requestType.setCountryId(1L);
        requestType.setCountryName("中国");
        requestType.setCityId(1L);
        requestType.setIgtCode("86");
        requestType.setDrvPhone("************");
        requestType.setAreaScope(0);
        requestType.setSupplierId(30804L);
//        Mockito.when(queryService.checkSkuIsExist(30804L)).thenReturn(Boolean.TRUE);
//        Mockito.when(repository.checkDrvOnly("1232Y7NA3123", TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())).thenReturn(Boolean.TRUE);
        Result<Boolean> result =  driverCommandService.addDrv(requestType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void addDrv1() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setDrvName("111");
        requestType.setDrvEnglishName("111");
        requestType.setDrvLanguage("EN");
        requestType.setCountryId(1L);
        requestType.setCountryName("中国");
        requestType.setCityId(1L);
        requestType.setIgtCode("86");
        requestType.setDrvPhone("************");
        requestType.setEmail("<EMAIL>");
        requestType.setAreaScope(0);
        requestType.setSupplierId(30804L);
//        Mockito.when(queryService.checkSkuIsExist(30804L)).thenReturn(Boolean.TRUE);
//        Mockito.when(repository.checkDrvOnly("<EMAIL>#", TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())).thenReturn(Boolean.TRUE);
        Result<Boolean> result =  driverCommandService.addDrv(requestType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void addDrv3() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setDrvName("111");
        requestType.setDrvEnglishName("111");
        requestType.setDrvLanguage("EN");
        requestType.setCountryId(1L);
        requestType.setCountryName("中国");
        requestType.setCityId(1L);
        requestType.setIgtCode("86");
        requestType.setDrvPhone("************");
        requestType.setDrvIdcard("333333");
        requestType.setEmail("<EMAIL>");
        requestType.setAreaScope(0);
        requestType.setSupplierId(30804L);
//        Mockito.when(queryService.checkSkuIsExist(30804L)).thenReturn(Boolean.TRUE);
//        Mockito.when(repository.checkDrvOnly("333333", TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())).thenReturn(Boolean.TRUE);
        Result<Boolean> result =  driverCommandService.addDrv(requestType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateDrv() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setInternalScope(0);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(po);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
//        Mockito.when(approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(1L,po,TmsTransportConstant.ApproveSourceTypeEnum.DRV,false,TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode(),Lists.newArrayList()))).thenReturn(false);
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void testAddDrvOfficial() throws SQLException {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setDrvPhone("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(po.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())).thenReturn(false);
        Result<Long> result = driverCommandService.addDrvOfficial(po);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void calculateUpdateDrvCoopMode() {
        List<Long> drvList = Arrays.asList(1L,2L);
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPOList.add(drvDriverPO);
//        Mockito.when(repository.queryDrvList(drvList)).thenReturn(drvDriverPOList);
        List<TransportGroupBasePO> groupBasePOList = Lists.newArrayList();
        TransportGroupBasePO basePO = new TransportGroupBasePO();
        basePO.setDrvId(2L);
        basePO.setTransportGroupMode(1006);
        basePO.setApplyStatus(2);
        basePO.setTransportGroupId(1L);
        groupBasePOList.add(basePO);
//        Mockito.when(driverGroupRelationRepository.queryDriverGroupRelationPO(drvList, Lists.newArrayList())).thenReturn(groupBasePOList);
        DrvInfoCacheDto drvInfoCacheDto = new DrvInfoCacheDto();
        drvInfoCacheDto.setCoopMode(4);
        drvInfoCacheDto.setBroadcast(1);
//        Mockito.when(driverQueryService.calculateDrvCoopMode(2L,groupBasePOList)).thenReturn(drvInfoCacheDto);
//        Mockito.when(repository.batchUpdateDrv(drvDriverPOList)).thenReturn(1);
        Boolean b = driverCommandService.calculateUpdateDrvCoopMode(drvList,"system");
        Assert.assertTrue(!b);
    }

    @Test
    public void commonCheckNew(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(0);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())).thenReturn(false);
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())).thenReturn(false);
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvIdcard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())).thenReturn(false);
            Mockito.when(vehicleRepository.checkVehAvailAndHasNoDrv(drvDriverPO.getVehicleId(), drvDriverPO.getSupplierId())).thenReturn(false);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(result.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void commonCheckNew1(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(0);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())).thenReturn(true);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(!result.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void commonCheckNew2(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(0);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())).thenReturn(true);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(!result.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void commonCheckNew3(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(0);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvIdcard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())).thenReturn(true);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(!result.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void commonCheckNew4(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(1);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
//            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())).thenReturn(true);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(!result.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void commonCheckNew5(){
        try {
            DrvDriverPO drvDriverPO = new DrvDriverPO();
            drvDriverPO.setDrvPhone("111");
            drvDriverPO.setVehicleId(1L);
            drvDriverPO.setSupplierId(1L);
            drvDriverPO.setInternalScope(1);
            drvDriverPO.setEmail("111");
            drvDriverPO.setDrvIdcard("111");
//            Mockito.when(drvDrvierRepository.checkDrvOnly(drvDriverPO.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())).thenReturn(false);
            Result<Long>  result =  driverCommandService.commonCheckNew(drvDriverPO,true);
            Assert.assertTrue(result.isSuccess());
        }catch (Exception e){

        }
    }


    @Test
    public void updateDrv1() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setInternalScope(0);
        po.setCategorySynthesizeCode(1);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(1L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(2);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateDrv2() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setInternalScope(0);
        po.setCategorySynthesizeCode(1);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(1L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(3);
//        Mockito.when(approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(1L,po,TmsTransportConstant.ApproveSourceTypeEnum.DRV,false,TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode(),Lists.newArrayList()))).thenReturn(false);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateDrv2MobileCheck() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setInternalScope(0);
        po.setCategorySynthesizeCode(1);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(1L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(3);
        //        Mockito.when(approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(1L,po,TmsTransportConstant.ApproveSourceTypeEnum.DRV,false,TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode(),Lists.newArrayList()))).thenReturn(false);
//        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().fail().withCode("error")
          .withData(false).build());
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
        Assert.assertTrue("mobile check failed:" + result.getCode(),result.getCode().equals("error"));
    }

    @Test
    public void updateDrv3() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(1L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setCategorySynthesizeCode(1);
        po.setSupplierId(2L);
        po.setInternalScope(1);
        po.setVersionFlag(4);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(2L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(3);
        orgDriver.setSupplierId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
//        Mockito.when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
//        Mockito.when(drvDrvierRepository.updateDrv(po)).thenReturn(1);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(productionLineUtil.getShowProductionLineList(any())).thenReturn(Lists.newArrayList(1));
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertEquals("1515010", result.getCode());
    }

    @Test
    public void updateDrv4() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(2L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setCategorySynthesizeCode(1);
        po.setSupplierId(1L);
        po.setInternalScope(1);
        po.setVersionFlag(4);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(2L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(3);
        orgDriver.setSupplierId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
        Mockito.when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        Mockito.when(drvDrvierRepository.updateDrv(po)).thenReturn(1);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(queryCategoryService.getContractList(any(), anyList())).thenReturn(Lists.newArrayList(new CategorySOADTO(3, "")));
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateDrv5() {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("111");
        po.setDrvEnglishName("111");
        po.setDrvLanguage("EN");
        po.setCountryId(1L);
        po.setCountryName("中国");
        po.setCityId(2L);
        po.setIgtCode("86");
        po.setActive(true);
        po.setDrvPhone("************");
        po.setCategorySynthesizeCode(1);
        po.setSupplierId(1L);
        po.setInternalScope(1);
        po.setVersionFlag(4);

        DrvDriverPO orgDriver = new DrvDriverPO();
        po.setDrvId(1L);
        orgDriver.setDrvName("111");
        orgDriver.setDrvEnglishName("111");
        orgDriver.setDrvLanguage("EN");
        orgDriver.setCountryId(1L);
        orgDriver.setCountryName("中国");
        orgDriver.setCityId(2L);
        orgDriver.setIgtCode("86");
        orgDriver.setActive(true);
        orgDriver.setDrvPhone("************");
        orgDriver.setInternalScope(0);
        orgDriver.setCategorySynthesizeCode(4);
        orgDriver.setDrvStatus(3);
        orgDriver.setSupplierId(1L);
        orgDriver.setVehicleId(1L);
        orgDriver.setVehicleTypeId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDriver);
        Mockito.when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        Mockito.when(drvDrvierRepository.updateDrv(po)).thenReturn(1);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(queryCategoryService.getContractList(any(), anyList())).thenReturn(Lists.newArrayList(new CategorySOADTO(3, "")));
        Result<Long> result =  driverCommandService.updateDrv(po,true,Lists.newArrayList());
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateDrvStatus1() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Arrays.asList(1L))).thenReturn(drvDriverPOList);
        Result Permission = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.judgeDrvOnlinePermission(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(Permission);
        Mockito.when(drvDrvierRepository.updateDrvStatus(Mockito.anyList(),Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString())).thenReturn(1);
        List<DriverAccountRegisterResultDTO> registerResultDTOList = Lists.newArrayList();
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setUid("111");
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTOList.add(registerResultDTO);
        Mockito.when(driverAccountManagementHelper.batchRegisterDriverAccountWhenNotExist(Mockito.anyList())).thenReturn(registerResultDTOList);
        Result result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Result<Boolean> result1 = driverCommandService.updateDrvStatus(Arrays.asList(1L),1,1,"system");
        Assert.assertTrue(result1.isSuccess());
    }

    @Test
    public void updateDrvStatusAsssertPhoneNotValid() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Arrays.asList(1L))).thenReturn(drvDriverPOList);
        Result Permission = Result.Builder.<Boolean>newResult().success().withData(true).build();
        List<DriverAccountRegisterResultDTO> registerResultDTOList = Lists.newArrayList();
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setUid("111");
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTOList.add(registerResultDTO);
        Mockito.when(mobileUtil.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().fail().build());
        Result<Boolean> result1 = driverCommandService.updateDrvStatus(Arrays.asList(1L),1,1,"system");
      Assert.assertFalse(result1.isSuccess());
    }

    @Test
    public void updateDrvStatus2() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Arrays.asList(1L))).thenReturn(drvDriverPOList);
        Result Permission = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.judgeDrvOnlinePermission(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(Permission);
        Mockito.when(drvDrvierRepository.updateDrvStatus(Mockito.anyList(),Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString())).thenReturn(1);
        List<DriverAccountRegisterResultDTO> registerResultDTOList = Lists.newArrayList();
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setUid("111");
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setRegisterSuccess(false);
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTOList.add(registerResultDTO);
        Mockito.when(driverAccountManagementHelper.batchRegisterDriverAccountWhenNotExist(Mockito.anyList())).thenReturn(registerResultDTOList);
        Result result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Result<Boolean> result1 = driverCommandService.updateDrvStatus(Arrays.asList(1L),1,1,"system");
        Assert.assertTrue(!result1.isSuccess());
    }

    @Test
    public void updateDrvStatus3() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
         drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(2L);
        drvDriverPO.setDrvStatus(3);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOList.add(drvDriverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Mockito.anyList())).thenReturn(drvDriverPOList);
        Result Permission = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.judgeDrvOnlinePermission(Mockito.anyBoolean(),Mockito.anyList(),Mockito.anyList())).thenReturn(Permission);
        Mockito.when(drvDrvierRepository.updateDrvStatus(Mockito.anyList(),Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString())).thenReturn(1);
        List<DriverAccountRegisterResultDTO> registerResultDTOList = Lists.newArrayList();
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setUid("111");
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTOList.add(registerResultDTO);
        registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setUid("111");
        registerResultDTO.setDrvId(2L);
        registerResultDTO.setRegisterSuccess(false);
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTOList.add(registerResultDTO);
        Mockito.when(driverAccountManagementHelper.batchRegisterDriverAccountWhenNotExist(Mockito.anyList())).thenReturn(registerResultDTOList);
        Result result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Result<Boolean> result1 = driverCommandService.updateDrvStatus(Arrays.asList(1L,2L),1,1,"system");
        Assert.assertTrue(!result1.isSuccess());
    }

}
